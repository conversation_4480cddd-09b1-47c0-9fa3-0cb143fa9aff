/**************************************************************************//**
 * @file
 * @brief EFR32FG23 PRS register signal bit field definitions
 ******************************************************************************
 * # License
 * <b>Copyright 2024 Silicon Laboratories, Inc. www.silabs.com</b>
 ******************************************************************************
 *
 * SPDX-License-Identifier: Zlib
 *
 * The licensor of this software is Silicon Laboratories Inc.
 *
 * This software is provided 'as-is', without any express or implied
 * warranty. In no event will the authors be held liable for any damages
 * arising from the use of this software.
 *
 * Permission is granted to anyone to use this software for any purpose,
 * including commercial applications, and to alter it and redistribute it
 * freely, subject to the following restrictions:
 *
 * 1. The origin of this software must not be misrepresented; you must not
 *    claim that you wrote the original software. If you use this software
 *    in a product, an acknowledgment in the product documentation would be
 *    appreciated but is not required.
 * 2. Altered source versions must be plainly marked as such, and must not be
 *    misrepresented as being the original software.
 * 3. This notice may not be removed or altered from any source distribution.
 *
 *****************************************************************************/
#ifndef EFR32FG23_PRS_SIGNALS_H
#define EFR32FG23_PRS_SIGNALS_H

/** Synchronous signal sources enumeration: */
#define _PRS_SYNC_CH_CTRL_SOURCESEL_NONE                   (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0                 (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_IADC0                  (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3                 (0x00000005UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4                 (0x00000006UL)
#define _PRS_SYNC_CH_CTRL_SOURCESEL_VDAC0                  (0x00000007UL)

/** Synchronous signal sources enumeration aligned with register bit field: */
#define PRS_SYNC_CH_CTRL_SOURCESEL_NONE                    (_PRS_SYNC_CH_CTRL_SOURCESEL_NONE << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0                  (_PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1                  (_PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_IADC0                   (_PRS_SYNC_CH_CTRL_SOURCESEL_IADC0 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2                  (_PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3                  (_PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4                  (_PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 << 8)
#define PRS_SYNC_CH_CTRL_SOURCESEL_VDAC0                   (_PRS_SYNC_CH_CTRL_SOURCESEL_VDAC0 << 8)

/** Synchronous signals enumeration: */
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER0UF                  (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER0OF                  (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC0                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC1                 (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER1UF                  (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER1OF                  (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC0                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC1                 (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE        (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE        (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE           (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER2UF                  (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER2OF                  (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC0                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC1                 (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER3UF                  (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER3OF                  (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC0                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC1                 (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER4UF                  (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER4OF                  (0x00000001UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC0                 (0x00000002UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC1                 (0x00000003UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC2                 (0x00000004UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH0DONESYNC          (0x00000000UL)
#define _PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH1DONESYNC          (0x00000001UL)

/** Synchronous signals enumeration aligned with register bit field: */
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER0UF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER0UF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER0OF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER0OF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC0                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC0 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC1                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC1 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC2                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC2 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER1UF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER1UF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER1OF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER1OF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC0                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC0 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC1                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC1 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC2                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC2 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE         (_PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE         (_PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE            (_PRS_SYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER2UF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER2UF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER2OF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER2OF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC0                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC0 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC1                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC1 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC2                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC2 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER3UF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER3UF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER3OF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER3OF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC0                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC0 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC1                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC1 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC2                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC2 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER4UF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER4UF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER4OF                   (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER4OF << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC0                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC0 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC1                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC1 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC2                  (_PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC2 << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH0DONESYNC           (_PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH0DONESYNC << 0)
#define PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH1DONESYNC           (_PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH1DONESYNC << 0)

/** Synchronous signals and sources combined and aligned with register bit fields: */
#define PRS_SYNC_TIMER0_UF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER0UF)
#define PRS_SYNC_TIMER0_OF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER0OF)
#define PRS_SYNC_TIMER0_CC0                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC0)
#define PRS_SYNC_TIMER0_CC1                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC1)
#define PRS_SYNC_TIMER0_CC2                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER0CC2)
#define PRS_SYNC_TIMER1_UF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER1UF)
#define PRS_SYNC_TIMER1_OF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER1OF)
#define PRS_SYNC_TIMER1_CC0                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC0)
#define PRS_SYNC_TIMER1_CC1                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC1)
#define PRS_SYNC_TIMER1_CC2                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER1CC2)
#define PRS_SYNC_IADC0_SCAN_ENTRY_DONE                     (PRS_SYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE)
#define PRS_SYNC_IADC0_SCAN_TABLE_DONE                     (PRS_SYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_SYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE)
#define PRS_SYNC_IADC0_SINGLE_DONE                         (PRS_SYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_SYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE)
#define PRS_SYNC_TIMER2_UF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER2UF)
#define PRS_SYNC_TIMER2_OF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER2OF)
#define PRS_SYNC_TIMER2_CC0                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC0)
#define PRS_SYNC_TIMER2_CC1                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC1)
#define PRS_SYNC_TIMER2_CC2                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER2CC2)
#define PRS_SYNC_TIMER3_UF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER3UF)
#define PRS_SYNC_TIMER3_OF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER3OF)
#define PRS_SYNC_TIMER3_CC0                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC0)
#define PRS_SYNC_TIMER3_CC1                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC1)
#define PRS_SYNC_TIMER3_CC2                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER3CC2)
#define PRS_SYNC_TIMER4_UF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER4UF)
#define PRS_SYNC_TIMER4_OF                                 (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER4OF)
#define PRS_SYNC_TIMER4_CC0                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC0)
#define PRS_SYNC_TIMER4_CC1                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC1)
#define PRS_SYNC_TIMER4_CC2                                (PRS_SYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_SYNC_CH_CTRL_SIGSEL_TIMER4CC2)
#define PRS_SYNC_VDAC0_CH0_DONE_SYNC                       (PRS_SYNC_CH_CTRL_SOURCESEL_VDAC0 | PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH0DONESYNC)
#define PRS_SYNC_VDAC0_CH1_DONE_SYNC                       (PRS_SYNC_CH_CTRL_SOURCESEL_VDAC0 | PRS_SYNC_CH_CTRL_SIGSEL_VDAC0CH1DONESYNC)

/** Asynchronous signal sources enumeration: */
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_NONE                  (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_LETIMER0              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_BURTC                 (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO                  (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL                  (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_CMU                   (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_CMUH                  (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL                  (0x00000008UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_PRS                   (0x00000009UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP0                 (0x0000000aUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP1                 (0x0000000bUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L                (0x0000000cUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0                 (0x0000000dUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_PCNT0                 (0x0000000eUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0               (0x0000000fUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE               (0x00000010UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0L                (0x00000011UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0                 (0x00000012UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L              (0x00000013UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0               (0x00000014UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EMUL                  (0x00000015UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EMU                   (0x00000016UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCOEM23             (0x00000017UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_LCD                   (0x00000018UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_USART0                (0x00000020UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0                (0x00000021UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1                (0x00000022UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2                (0x00000023UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3                (0x00000024UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_CORE                  (0x00000025UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL                  (0x00000026UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_AGC                   (0x00000027UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC                  (0x00000028UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML                (0x00000029UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM                 (0x0000002aUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH                (0x0000002bUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_FRC                   (0x0000002cUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL             (0x0000002dUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER              (0x0000002eUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_SYNTH                 (0x0000002fUL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_RACL                  (0x00000030UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_RAC                   (0x00000031UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4                (0x00000032UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L              (0x00000033UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1               (0x00000034UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L              (0x00000035UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2               (0x00000036UL)
#define _PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCO0                (0x00000037UL)

/** Asynchronous signal sources enumeration aligned with register bit field: */
#define PRS_ASYNC_CH_CTRL_SOURCESEL_NONE                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_NONE << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_USART0                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_LETIMER0               (_PRS_ASYNC_CH_CTRL_SOURCESEL_LETIMER0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_BURTC                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_BURTC << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_CORE                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_CORE << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_CMU                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_CMU << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_CMUH                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_CMUH << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_AGC                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_AGC << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_FRC                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_FRC << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL              (_PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER               (_PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_SYNTH                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_SYNTH << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_PRS                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_PRS << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_RACL                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_RACL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_RAC                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_RAC << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP0                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP1                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP1 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_PCNT0                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_PCNT0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0                (_PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE                (_PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0L                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0L << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0                  (_PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L               (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0                (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L               (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1                (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L               (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2                (_PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCO0                 (_PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCO0 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EMUL                   (_PRS_ASYNC_CH_CTRL_SOURCESEL_EMUL << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_EMU                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_EMU << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCOEM23              (_PRS_ASYNC_CH_CTRL_SOURCESEL_HFRCOEM23 << 8)
#define PRS_ASYNC_CH_CTRL_SOURCESEL_LCD                    (_PRS_ASYNC_CH_CTRL_SOURCESEL_LCD << 8)

/** Asynchronous signals enumeration: */
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0CS                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0IRTX               (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0RTS                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0RXDATA             (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0TX                 (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_USART0TXC                (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0UF                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0OF                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC0                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC1                (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC2                (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1UF                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1OF                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC0                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC1                (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC2                (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE       (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE       (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE          (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH0              (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH1              (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BURTCCOMP                (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BURTCOVERFLOW            (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN0                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN1                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN2                 (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN3                 (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN4                 (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN5                 (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN6                 (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN7                 (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2UF                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2OF                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC0                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC1                (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC2                (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3UF                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3OF                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC0                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC1                (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC2                (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT0              (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT1              (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT2              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT3              (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT0              (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT1              (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT2              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCA                  (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCAREQ               (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINADJUST           (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINOK               (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINREDUCED          (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKI1               (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKQ2               (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKRST              (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCPEAKDET               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCPROPAGATED            (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_AGCRSSIDONE              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR0                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR1                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR2                 (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR3                 (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT0                 (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT1                 (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_BUFCFULL                 (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLADVANCE            (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT0               (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT1               (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSADET          (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSALIVE         (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDCLK               (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDOUT               (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLFRAMEDET           (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMFRAMESENT           (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLOWCORR             (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSADET            (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSALIVE           (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWSYMBOL           (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWWND              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPOSTPONE            (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPREDET              (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHPRESENT            (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHRSSIJUMP           (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHSYNCSENT           (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHTIMDET             (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHWEAK               (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHEOF                (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_FRCDCLK                  (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_FRCDOUT                  (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBOF             (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC0             (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC1             (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC2             (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC3             (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC4             (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTF            (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTR            (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBTS             (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERPOF              (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0MATCH          (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0UF             (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1MATCH          (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1UF             (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERWOF              (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX0                (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX1                (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH0              (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH1              (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH2              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH3              (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH4              (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH5              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH6              (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH7              (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH8               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH9               (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH10              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH11              (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLACTIVE               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLLNAEN                (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLPAEN                 (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLRX                   (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLTX                   (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT0              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT1              (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT2              (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACCTIOUT3               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATA            (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATAVALID       (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4UF                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4OF                 (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC0                (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC1                (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC2                (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_ACMP0OUT                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_ACMP1OUT                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0WARM            (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1WARM            (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0DONEASYNC       (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1DONEASYNC       (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LINTERNALTIMEROF    (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LREFRESHTIMEROF     (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0DIR                 (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0UFOF                (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT0          (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT1          (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT0          (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT1          (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT0           (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT1           (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT2           (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECCMP            (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS             (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS1            (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LCS               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LIRDATX           (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRTS              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXDATAV          (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTX               (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXC              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXFL             (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXFL             (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LCS               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LIRDATX           (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRTS              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXDATAV          (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTX               (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXC              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXFL             (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXFL             (0x00000007UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LCS               (0x00000000UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LIRDATX           (0x00000001UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRTS              (0x00000002UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXDATAV          (0x00000003UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTX               (0x00000004UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXC              (0x00000005UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXFL             (0x00000006UL)
#define _PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXFL             (0x00000007UL)

/** Asynchronous signals enumeration aligned with register bit field: */
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0CS                  (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0CS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0IRTX                (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0IRTX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0RTS                 (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0RTS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0RXDATA              (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0RXDATA << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0TX                  (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0TX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_USART0TXC                 (_PRS_ASYNC_CH_CTRL_SIGSEL_USART0TXC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0UF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0OF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0OF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC2                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1UF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1OF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1OF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC2                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE        (_PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE        (_PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE           (_PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH0               (_PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH1               (_PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BURTCCOMP                 (_PRS_ASYNC_CH_CTRL_SIGSEL_BURTCCOMP << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BURTCOVERFLOW             (_PRS_ASYNC_CH_CTRL_SIGSEL_BURTCOVERFLOW << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN0                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN1                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN2                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN3                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN4                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN4 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN5                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN5 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN6                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN6 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN7                  (_PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN7 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2UF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2OF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2OF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC2                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3UF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3OF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3OF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC2                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT0               (_PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT1               (_PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT2               (_PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT3               (_PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT0               (_PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT1               (_PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT2               (_PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCA                   (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCA << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCAREQ                (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCAREQ << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINADJUST            (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINADJUST << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINOK                (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINOK << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINREDUCED           (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINREDUCED << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKI1                (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKI1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKQ2                (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKQ2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKRST               (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKRST << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCPEAKDET                (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCPEAKDET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCPROPAGATED             (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCPROPAGATED << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_AGCRSSIDONE               (_PRS_ASYNC_CH_CTRL_SIGSEL_AGCRSSIDONE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR0                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR1                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR2                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR3                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT0                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT1                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_BUFCFULL                  (_PRS_ASYNC_CH_CTRL_SIGSEL_BUFCFULL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLADVANCE             (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLADVANCE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT0                (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT1                (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSADET           (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSADET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSALIVE          (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSALIVE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDCLK                (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDCLK << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDOUT                (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDOUT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLFRAMEDET            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLFRAMEDET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMFRAMESENT            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMFRAMESENT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLOWCORR              (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLOWCORR << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSADET             (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSADET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSALIVE            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSALIVE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWSYMBOL            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWSYMBOL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWWND               (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWWND << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPOSTPONE             (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPOSTPONE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPREDET               (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPREDET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHPRESENT             (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHPRESENT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHRSSIJUMP            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHRSSIJUMP << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHSYNCSENT            (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHSYNCSENT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHTIMDET              (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHTIMDET << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHWEAK                (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHWEAK << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHEOF                 (_PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHEOF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_FRCDCLK                   (_PRS_ASYNC_CH_CTRL_SIGSEL_FRCDCLK << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_FRCDOUT                   (_PRS_ASYNC_CH_CTRL_SIGSEL_FRCDOUT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBOF              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBOF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC0              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC1              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC2              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC3              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC4              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC4 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTF             (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTR             (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTR << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBTS              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBTS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERPOF               (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERPOF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0MATCH           (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0MATCH << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0UF              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1MATCH           (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1MATCH << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1UF              (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERWOF               (_PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERWOF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH0               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH1               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH2               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH3               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH4               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH4 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH5               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH5 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH6               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH6 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH7               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH7 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH8                (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH8 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH9                (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH9 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH10               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH10 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH11               (_PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH11 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLACTIVE                (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLACTIVE << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLLNAEN                 (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLLNAEN << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLPAEN                  (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLPAEN << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLRX                    (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLRX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLTX                    (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLTX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT0               (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT1               (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT2               (_PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACCTIOUT3                (_PRS_ASYNC_CH_CTRL_SIGSEL_RACCTIOUT3 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATA             (_PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATA << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATAVALID        (_PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATAVALID << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4UF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4UF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4OF                  (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4OF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC0                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC1                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC2                 (_PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_ACMP0OUT                  (_PRS_ASYNC_CH_CTRL_SIGSEL_ACMP0OUT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_ACMP1OUT                  (_PRS_ASYNC_CH_CTRL_SIGSEL_ACMP1OUT << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0WARM             (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0WARM << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1WARM             (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1WARM << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0DONEASYNC        (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0DONEASYNC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1DONEASYNC        (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1DONEASYNC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LINTERNALTIMEROF     (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LINTERNALTIMEROF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LREFRESHTIMEROF      (_PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LREFRESHTIMEROF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0DIR                  (_PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0DIR << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0UFOF                 (_PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0UFOF << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT0           (_PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT1           (_PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT0           (_PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT1           (_PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT0            (_PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT0 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT1            (_PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT2            (_PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT2 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECCMP             (_PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECCMP << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS              (_PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS1             (_PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS1 << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LCS                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LCS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LIRDATX            (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LIRDATX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRTS               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRTS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXDATAV           (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXDATAV << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTX                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXC               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXFL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXFL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LCS                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LCS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LIRDATX            (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LIRDATX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRTS               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRTS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXDATAV           (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXDATAV << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTX                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXC               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXFL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXFL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LCS                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LCS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LIRDATX            (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LIRDATX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRTS               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRTS << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXDATAV           (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXDATAV << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTX                (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTX << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXC               (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXC << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXFL << 0)
#define PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXFL              (_PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXFL << 0)

/** Asynchronous signals and sources combined and aligned with register bit fields: */
#define PRS_ASYNC_USART0_CS                                (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0CS)
#define PRS_ASYNC_USART0_IRTX                              (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0IRTX)
#define PRS_ASYNC_USART0_RTS                               (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0RTS)
#define PRS_ASYNC_USART0_RXDATA                            (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0RXDATA)
#define PRS_ASYNC_USART0_TX                                (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0TX)
#define PRS_ASYNC_USART0_TXC                               (PRS_ASYNC_CH_CTRL_SOURCESEL_USART0 | PRS_ASYNC_CH_CTRL_SIGSEL_USART0TXC)
#define PRS_ASYNC_TIMER0_UF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0UF)
#define PRS_ASYNC_TIMER0_OF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0OF)
#define PRS_ASYNC_TIMER0_CC0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC0)
#define PRS_ASYNC_TIMER0_CC1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC1)
#define PRS_ASYNC_TIMER0_CC2                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER0CC2)
#define PRS_ASYNC_TIMER1_UF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1UF)
#define PRS_ASYNC_TIMER1_OF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1OF)
#define PRS_ASYNC_TIMER1_CC0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC0)
#define PRS_ASYNC_TIMER1_CC1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC1)
#define PRS_ASYNC_TIMER1_CC2                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER1 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER1CC2)
#define PRS_ASYNC_IADC0_SCANENTRYDONE                      (PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANENTRYDONE)
#define PRS_ASYNC_IADC0_SCANTABLEDONE                      (PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SCANTABLEDONE)
#define PRS_ASYNC_IADC0_SINGLEDONE                         (PRS_ASYNC_CH_CTRL_SOURCESEL_IADC0 | PRS_ASYNC_CH_CTRL_SIGSEL_IADC0SINGLEDONE)
#define PRS_ASYNC_LETIMER0_CH0                             (PRS_ASYNC_CH_CTRL_SOURCESEL_LETIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH0)
#define PRS_ASYNC_LETIMER0_CH1                             (PRS_ASYNC_CH_CTRL_SOURCESEL_LETIMER0 | PRS_ASYNC_CH_CTRL_SIGSEL_LETIMER0CH1)
#define PRS_ASYNC_BURTC_COMP                               (PRS_ASYNC_CH_CTRL_SOURCESEL_BURTC | PRS_ASYNC_CH_CTRL_SIGSEL_BURTCCOMP)
#define PRS_ASYNC_BURTC_OVERFLOW                           (PRS_ASYNC_CH_CTRL_SOURCESEL_BURTC | PRS_ASYNC_CH_CTRL_SIGSEL_BURTCOVERFLOW)
#define PRS_ASYNC_GPIO_PIN0                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN0)
#define PRS_ASYNC_GPIO_PIN1                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN1)
#define PRS_ASYNC_GPIO_PIN2                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN2)
#define PRS_ASYNC_GPIO_PIN3                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN3)
#define PRS_ASYNC_GPIO_PIN4                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN4)
#define PRS_ASYNC_GPIO_PIN5                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN5)
#define PRS_ASYNC_GPIO_PIN6                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN6)
#define PRS_ASYNC_GPIO_PIN7                                (PRS_ASYNC_CH_CTRL_SOURCESEL_GPIO | PRS_ASYNC_CH_CTRL_SIGSEL_GPIOPIN7)
#define PRS_ASYNC_TIMER2_UF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2UF)
#define PRS_ASYNC_TIMER2_OF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2OF)
#define PRS_ASYNC_TIMER2_CC0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC0)
#define PRS_ASYNC_TIMER2_CC1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC1)
#define PRS_ASYNC_TIMER2_CC2                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER2 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER2CC2)
#define PRS_ASYNC_TIMER3_UF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3UF)
#define PRS_ASYNC_TIMER3_OF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3OF)
#define PRS_ASYNC_TIMER3_CC0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC0)
#define PRS_ASYNC_TIMER3_CC1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC1)
#define PRS_ASYNC_TIMER3_CC2                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER3 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER3CC2)
#define PRS_ASYNC_CORE_CTIOUT0                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CORE | PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT0)
#define PRS_ASYNC_CORE_CTIOUT1                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CORE | PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT1)
#define PRS_ASYNC_CORE_CTIOUT2                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CORE | PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT2)
#define PRS_ASYNC_CORE_CTIOUT3                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CORE | PRS_ASYNC_CH_CTRL_SIGSEL_CORECTIOUT3)
#define PRS_ASYNC_CMUL_CLKOUT0                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL | PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT0)
#define PRS_ASYNC_CMUL_CLKOUT1                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL | PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT1)
#define PRS_ASYNC_CMUL_CLKOUT2                             (PRS_ASYNC_CH_CTRL_SOURCESEL_CMUL | PRS_ASYNC_CH_CTRL_SIGSEL_CMULCLKOUT2)
#define PRS_ASYNC_AGCL_CCA                                 (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCA)
#define PRS_ASYNC_AGCL_CCAREQ                              (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLCCAREQ)
#define PRS_ASYNC_AGCL_GAINADJUST                          (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINADJUST)
#define PRS_ASYNC_AGCL_GAINOK                              (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINOK)
#define PRS_ASYNC_AGCL_GAINREDUCED                         (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLGAINREDUCED)
#define PRS_ASYNC_AGCL_IFPKI1                              (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKI1)
#define PRS_ASYNC_AGCL_IFPKQ2                              (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKQ2)
#define PRS_ASYNC_AGCL_IFPKRST                             (PRS_ASYNC_CH_CTRL_SOURCESEL_AGCL | PRS_ASYNC_CH_CTRL_SIGSEL_AGCLIFPKRST)
#define PRS_ASYNC_AGC_PEAKDET                              (PRS_ASYNC_CH_CTRL_SOURCESEL_AGC | PRS_ASYNC_CH_CTRL_SIGSEL_AGCPEAKDET)
#define PRS_ASYNC_AGC_PROPAGATED                           (PRS_ASYNC_CH_CTRL_SOURCESEL_AGC | PRS_ASYNC_CH_CTRL_SIGSEL_AGCPROPAGATED)
#define PRS_ASYNC_AGC_RSSIDONE                             (PRS_ASYNC_CH_CTRL_SOURCESEL_AGC | PRS_ASYNC_CH_CTRL_SIGSEL_AGCRSSIDONE)
#define PRS_ASYNC_BUFC_THR0                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR0)
#define PRS_ASYNC_BUFC_THR1                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR1)
#define PRS_ASYNC_BUFC_THR2                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR2)
#define PRS_ASYNC_BUFC_THR3                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCTHR3)
#define PRS_ASYNC_BUFC_CNT0                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT0)
#define PRS_ASYNC_BUFC_CNT1                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCCNT1)
#define PRS_ASYNC_BUFC_FULL                                (PRS_ASYNC_CH_CTRL_SOURCESEL_BUFC | PRS_ASYNC_CH_CTRL_SIGSEL_BUFCFULL)
#define PRS_ASYNC_MODEML_ADVANCE                           (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLADVANCE)
#define PRS_ASYNC_MODEML_ANT0                              (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT0)
#define PRS_ASYNC_MODEML_ANT1                              (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLANT1)
#define PRS_ASYNC_MODEML_COHDSADET                         (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSADET)
#define PRS_ASYNC_MODEML_COHDSALIVE                        (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLCOHDSALIVE)
#define PRS_ASYNC_MODEML_DCLK                              (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDCLK)
#define PRS_ASYNC_MODEML_DOUT                              (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLDOUT)
#define PRS_ASYNC_MODEML_FRAMEDET                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEML | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLFRAMEDET)
#define PRS_ASYNC_MODEM_FRAMESENT                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMFRAMESENT)
#define PRS_ASYNC_MODEM_LOWCORR                            (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLOWCORR)
#define PRS_ASYNC_MODEM_LRDSADET                           (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSADET)
#define PRS_ASYNC_MODEM_LRDSALIVE                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMLRDSALIVE)
#define PRS_ASYNC_MODEM_NEWSYMBOL                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWSYMBOL)
#define PRS_ASYNC_MODEM_NEWWND                             (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMNEWWND)
#define PRS_ASYNC_MODEM_POSTPONE                           (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPOSTPONE)
#define PRS_ASYNC_MODEM_PREDET                             (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEM | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMPREDET)
#define PRS_ASYNC_MODEMH_PRESENT                           (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHPRESENT)
#define PRS_ASYNC_MODEMH_RSSIJUMP                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHRSSIJUMP)
#define PRS_ASYNC_MODEMH_SYNCSENT                          (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHSYNCSENT)
#define PRS_ASYNC_MODEMH_TIMDET                            (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHTIMDET)
#define PRS_ASYNC_MODEMH_WEAK                              (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHWEAK)
#define PRS_ASYNC_MODEMH_EOF                               (PRS_ASYNC_CH_CTRL_SOURCESEL_MODEMH | PRS_ASYNC_CH_CTRL_SIGSEL_MODEMHEOF)
#define PRS_ASYNC_FRC_DCLK                                 (PRS_ASYNC_CH_CTRL_SOURCESEL_FRC | PRS_ASYNC_CH_CTRL_SIGSEL_FRCDCLK)
#define PRS_ASYNC_FRC_DOUT                                 (PRS_ASYNC_CH_CTRL_SOURCESEL_FRC | PRS_ASYNC_CH_CTRL_SIGSEL_FRCDOUT)
#define PRS_ASYNC_PROTIMERL_BOF                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBOF)
#define PRS_ASYNC_PROTIMERL_CC0                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC0)
#define PRS_ASYNC_PROTIMERL_CC1                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC1)
#define PRS_ASYNC_PROTIMERL_CC2                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC2)
#define PRS_ASYNC_PROTIMERL_CC3                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC3)
#define PRS_ASYNC_PROTIMERL_CC4                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLCC4)
#define PRS_ASYNC_PROTIMERL_LBTF                           (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTF)
#define PRS_ASYNC_PROTIMERL_LBTR                           (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMERL | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLLBTR)
#define PRS_ASYNC_PROTIMER_LBTS                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERLBTS)
#define PRS_ASYNC_PROTIMER_POF                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERPOF)
#define PRS_ASYNC_PROTIMER_T0MATCH                         (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0MATCH)
#define PRS_ASYNC_PROTIMER_T0UF                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT0UF)
#define PRS_ASYNC_PROTIMER_T1MATCH                         (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1MATCH)
#define PRS_ASYNC_PROTIMER_T1UF                            (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERT1UF)
#define PRS_ASYNC_PROTIMER_WOF                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PROTIMER | PRS_ASYNC_CH_CTRL_SIGSEL_PROTIMERWOF)
#define PRS_ASYNC_SYNTH_MUX0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_SYNTH | PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX0)
#define PRS_ASYNC_SYNTH_MUX1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_SYNTH | PRS_ASYNC_CH_CTRL_SIGSEL_SYNTHMUX1)
#define PRS_ASYNC_PRSL_ASYNCH0                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH0)
#define PRS_ASYNC_PRSL_ASYNCH1                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH1)
#define PRS_ASYNC_PRSL_ASYNCH2                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH2)
#define PRS_ASYNC_PRSL_ASYNCH3                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH3)
#define PRS_ASYNC_PRSL_ASYNCH4                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH4)
#define PRS_ASYNC_PRSL_ASYNCH5                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH5)
#define PRS_ASYNC_PRSL_ASYNCH6                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH6)
#define PRS_ASYNC_PRSL_ASYNCH7                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRSL | PRS_ASYNC_CH_CTRL_SIGSEL_PRSLASYNCH7)
#define PRS_ASYNC_PRS_ASYNCH8                              (PRS_ASYNC_CH_CTRL_SOURCESEL_PRS | PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH8)
#define PRS_ASYNC_PRS_ASYNCH9                              (PRS_ASYNC_CH_CTRL_SOURCESEL_PRS | PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH9)
#define PRS_ASYNC_PRS_ASYNCH10                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRS | PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH10)
#define PRS_ASYNC_PRS_ASYNCH11                             (PRS_ASYNC_CH_CTRL_SOURCESEL_PRS | PRS_ASYNC_CH_CTRL_SIGSEL_PRSASYNCH11)
#define PRS_ASYNC_RACL_ACTIVE                              (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLACTIVE)
#define PRS_ASYNC_RACL_LNAEN                               (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLLNAEN)
#define PRS_ASYNC_RACL_PAEN                                (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLPAEN)
#define PRS_ASYNC_RACL_RX                                  (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLRX)
#define PRS_ASYNC_RACL_TX                                  (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLTX)
#define PRS_ASYNC_RACL_CTIOUT0                             (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT0)
#define PRS_ASYNC_RACL_CTIOUT1                             (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT1)
#define PRS_ASYNC_RACL_CTIOUT2                             (PRS_ASYNC_CH_CTRL_SOURCESEL_RACL | PRS_ASYNC_CH_CTRL_SIGSEL_RACLCTIOUT2)
#define PRS_ASYNC_RAC_CTIOUT3                              (PRS_ASYNC_CH_CTRL_SOURCESEL_RAC | PRS_ASYNC_CH_CTRL_SIGSEL_RACCTIOUT3)
#define PRS_ASYNC_RAC_AUXADCDATA                           (PRS_ASYNC_CH_CTRL_SOURCESEL_RAC | PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATA)
#define PRS_ASYNC_RAC_AUXADCDATAVALID                      (PRS_ASYNC_CH_CTRL_SOURCESEL_RAC | PRS_ASYNC_CH_CTRL_SIGSEL_RACAUXADCDATAVALID)
#define PRS_ASYNC_TIMER4_UF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4UF)
#define PRS_ASYNC_TIMER4_OF                                (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4OF)
#define PRS_ASYNC_TIMER4_CC0                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC0)
#define PRS_ASYNC_TIMER4_CC1                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC1)
#define PRS_ASYNC_TIMER4_CC2                               (PRS_ASYNC_CH_CTRL_SOURCESEL_TIMER4 | PRS_ASYNC_CH_CTRL_SIGSEL_TIMER4CC2)
#define PRS_ASYNC_ACMP0_OUT                                (PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP0 | PRS_ASYNC_CH_CTRL_SIGSEL_ACMP0OUT)
#define PRS_ASYNC_ACMP1_OUT                                (PRS_ASYNC_CH_CTRL_SOURCESEL_ACMP1 | PRS_ASYNC_CH_CTRL_SIGSEL_ACMP1OUT)
#define PRS_ASYNC_VDAC0L_CH0WARM                           (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0WARM)
#define PRS_ASYNC_VDAC0L_CH1WARM                           (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1WARM)
#define PRS_ASYNC_VDAC0L_CH0DONEASYNC                      (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH0DONEASYNC)
#define PRS_ASYNC_VDAC0L_CH1DONEASYNC                      (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LCH1DONEASYNC)
#define PRS_ASYNC_VDAC0L_INTERNALTIMEROF                   (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LINTERNALTIMEROF)
#define PRS_ASYNC_VDAC0L_REFRESHTIMEROF                    (PRS_ASYNC_CH_CTRL_SOURCESEL_VDAC0L | PRS_ASYNC_CH_CTRL_SIGSEL_VDAC0LREFRESHTIMEROF)
#define PRS_ASYNC_PCNT0_DIR                                (PRS_ASYNC_CH_CTRL_SOURCESEL_PCNT0 | PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0DIR)
#define PRS_ASYNC_PCNT0_UFOF                               (PRS_ASYNC_CH_CTRL_SOURCESEL_PCNT0 | PRS_ASYNC_CH_CTRL_SIGSEL_PCNT0UFOF)
#define PRS_ASYNC_SYSRTC0_GRP0OUT0                         (PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0 | PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT0)
#define PRS_ASYNC_SYSRTC0_GRP0OUT1                         (PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0 | PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP0OUT1)
#define PRS_ASYNC_SYSRTC0_GRP1OUT0                         (PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0 | PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT0)
#define PRS_ASYNC_SYSRTC0_GRP1OUT1                         (PRS_ASYNC_CH_CTRL_SOURCESEL_SYSRTC0 | PRS_ASYNC_CH_CTRL_SIGSEL_SYSRTC0GRP1OUT1)
#define PRS_ASYNC_LESENSE_DECOUT0                          (PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE | PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT0)
#define PRS_ASYNC_LESENSE_DECOUT1                          (PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE | PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT1)
#define PRS_ASYNC_LESENSE_DECOUT2                          (PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE | PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECOUT2)
#define PRS_ASYNC_LESENSE_DECCMP                           (PRS_ASYNC_CH_CTRL_SOURCESEL_LESENSE | PRS_ASYNC_CH_CTRL_SIGSEL_LESENSEDECCMP)
#define PRS_ASYNC_HFXO0L_STATUS                            (PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0L | PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS)
#define PRS_ASYNC_HFXO0L_STATUS1                           (PRS_ASYNC_CH_CTRL_SOURCESEL_HFXO0L | PRS_ASYNC_CH_CTRL_SIGSEL_HFXO0LSTATUS1)
#define PRS_ASYNC_EUSART0L_CS                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LCS)
#define PRS_ASYNC_EUSART0L_IRDATX                          (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LIRDATX)
#define PRS_ASYNC_EUSART0L_RTS                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRTS)
#define PRS_ASYNC_EUSART0L_RXDATAV                         (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXDATAV)
#define PRS_ASYNC_EUSART0L_TX                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTX)
#define PRS_ASYNC_EUSART0L_TXC                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXC)
#define PRS_ASYNC_EUSART0L_RXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LRXFL)
#define PRS_ASYNC_EUSART0L_TXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART0L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART0LTXFL)
#define PRS_ASYNC_EUSART1L_CS                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LCS)
#define PRS_ASYNC_EUSART1L_IRDATX                          (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LIRDATX)
#define PRS_ASYNC_EUSART1L_RTS                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRTS)
#define PRS_ASYNC_EUSART1L_RXDATAV                         (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXDATAV)
#define PRS_ASYNC_EUSART1L_TX                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTX)
#define PRS_ASYNC_EUSART1L_TXC                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXC)
#define PRS_ASYNC_EUSART1L_RXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LRXFL)
#define PRS_ASYNC_EUSART1L_TXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART1L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART1LTXFL)
#define PRS_ASYNC_EUSART2L_CS                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LCS)
#define PRS_ASYNC_EUSART2L_IRDATX                          (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LIRDATX)
#define PRS_ASYNC_EUSART2L_RTS                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRTS)
#define PRS_ASYNC_EUSART2L_RXDATAV                         (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXDATAV)
#define PRS_ASYNC_EUSART2L_TX                              (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTX)
#define PRS_ASYNC_EUSART2L_TXC                             (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXC)
#define PRS_ASYNC_EUSART2L_RXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LRXFL)
#define PRS_ASYNC_EUSART2L_TXFL                            (PRS_ASYNC_CH_CTRL_SOURCESEL_EUSART2L | PRS_ASYNC_CH_CTRL_SIGSEL_EUSART2LTXFL)

/**
 * Asynchronous signals and sources combined and aligned with register bit fields
 * without the '_ASYNCH_' infix in order for backward compatibility:
 */
#define PRS_USART0_CS                                      (PRS_ASYNC_USART0_CS)
#define PRS_USART0_IRTX                                    (PRS_ASYNC_USART0_IRTX)
#define PRS_USART0_RTS                                     (PRS_ASYNC_USART0_RTS)
#define PRS_USART0_RXDATA                                  (PRS_ASYNC_USART0_RXDATA)
#define PRS_USART0_TX                                      (PRS_ASYNC_USART0_TX)
#define PRS_USART0_TXC                                     (PRS_ASYNC_USART0_TXC)
#define PRS_TIMER0_UF                                      (PRS_ASYNC_TIMER0_UF)
#define PRS_TIMER0_OF                                      (PRS_ASYNC_TIMER0_OF)
#define PRS_TIMER0_CC0                                     (PRS_ASYNC_TIMER0_CC0)
#define PRS_TIMER0_CC1                                     (PRS_ASYNC_TIMER0_CC1)
#define PRS_TIMER0_CC2                                     (PRS_ASYNC_TIMER0_CC2)
#define PRS_TIMER1_UF                                      (PRS_ASYNC_TIMER1_UF)
#define PRS_TIMER1_OF                                      (PRS_ASYNC_TIMER1_OF)
#define PRS_TIMER1_CC0                                     (PRS_ASYNC_TIMER1_CC0)
#define PRS_TIMER1_CC1                                     (PRS_ASYNC_TIMER1_CC1)
#define PRS_TIMER1_CC2                                     (PRS_ASYNC_TIMER1_CC2)
#define PRS_IADC0_SCANENTRYDONE                            (PRS_ASYNC_IADC0_SCANENTRYDONE)
#define PRS_IADC0_SCANTABLEDONE                            (PRS_ASYNC_IADC0_SCANTABLEDONE)
#define PRS_IADC0_SINGLEDONE                               (PRS_ASYNC_IADC0_SINGLEDONE)
#define PRS_LETIMER0_CH0                                   (PRS_ASYNC_LETIMER0_CH0)
#define PRS_LETIMER0_CH1                                   (PRS_ASYNC_LETIMER0_CH1)
#define PRS_BURTC_COMP                                     (PRS_ASYNC_BURTC_COMP)
#define PRS_BURTC_OVERFLOW                                 (PRS_ASYNC_BURTC_OVERFLOW)
#define PRS_GPIO_PIN0                                      (PRS_ASYNC_GPIO_PIN0)
#define PRS_GPIO_PIN1                                      (PRS_ASYNC_GPIO_PIN1)
#define PRS_GPIO_PIN2                                      (PRS_ASYNC_GPIO_PIN2)
#define PRS_GPIO_PIN3                                      (PRS_ASYNC_GPIO_PIN3)
#define PRS_GPIO_PIN4                                      (PRS_ASYNC_GPIO_PIN4)
#define PRS_GPIO_PIN5                                      (PRS_ASYNC_GPIO_PIN5)
#define PRS_GPIO_PIN6                                      (PRS_ASYNC_GPIO_PIN6)
#define PRS_GPIO_PIN7                                      (PRS_ASYNC_GPIO_PIN7)
#define PRS_TIMER2_UF                                      (PRS_ASYNC_TIMER2_UF)
#define PRS_TIMER2_OF                                      (PRS_ASYNC_TIMER2_OF)
#define PRS_TIMER2_CC0                                     (PRS_ASYNC_TIMER2_CC0)
#define PRS_TIMER2_CC1                                     (PRS_ASYNC_TIMER2_CC1)
#define PRS_TIMER2_CC2                                     (PRS_ASYNC_TIMER2_CC2)
#define PRS_TIMER3_UF                                      (PRS_ASYNC_TIMER3_UF)
#define PRS_TIMER3_OF                                      (PRS_ASYNC_TIMER3_OF)
#define PRS_TIMER3_CC0                                     (PRS_ASYNC_TIMER3_CC0)
#define PRS_TIMER3_CC1                                     (PRS_ASYNC_TIMER3_CC1)
#define PRS_TIMER3_CC2                                     (PRS_ASYNC_TIMER3_CC2)
#define PRS_CORE_CTIOUT0                                   (PRS_ASYNC_CORE_CTIOUT0)
#define PRS_CORE_CTIOUT1                                   (PRS_ASYNC_CORE_CTIOUT1)
#define PRS_CORE_CTIOUT2                                   (PRS_ASYNC_CORE_CTIOUT2)
#define PRS_CORE_CTIOUT3                                   (PRS_ASYNC_CORE_CTIOUT3)
#define PRS_CMUL_CLKOUT0                                   (PRS_ASYNC_CMUL_CLKOUT0)
#define PRS_CMUL_CLKOUT1                                   (PRS_ASYNC_CMUL_CLKOUT1)
#define PRS_CMUL_CLKOUT2                                   (PRS_ASYNC_CMUL_CLKOUT2)
#define PRS_AGCL_CCA                                       (PRS_ASYNC_AGCL_CCA)
#define PRS_AGCL_CCAREQ                                    (PRS_ASYNC_AGCL_CCAREQ)
#define PRS_AGCL_GAINADJUST                                (PRS_ASYNC_AGCL_GAINADJUST)
#define PRS_AGCL_GAINOK                                    (PRS_ASYNC_AGCL_GAINOK)
#define PRS_AGCL_GAINREDUCED                               (PRS_ASYNC_AGCL_GAINREDUCED)
#define PRS_AGCL_IFPKI1                                    (PRS_ASYNC_AGCL_IFPKI1)
#define PRS_AGCL_IFPKQ2                                    (PRS_ASYNC_AGCL_IFPKQ2)
#define PRS_AGCL_IFPKRST                                   (PRS_ASYNC_AGCL_IFPKRST)
#define PRS_AGC_PEAKDET                                    (PRS_ASYNC_AGC_PEAKDET)
#define PRS_AGC_PROPAGATED                                 (PRS_ASYNC_AGC_PROPAGATED)
#define PRS_AGC_RSSIDONE                                   (PRS_ASYNC_AGC_RSSIDONE)
#define PRS_BUFC_THR0                                      (PRS_ASYNC_BUFC_THR0)
#define PRS_BUFC_THR1                                      (PRS_ASYNC_BUFC_THR1)
#define PRS_BUFC_THR2                                      (PRS_ASYNC_BUFC_THR2)
#define PRS_BUFC_THR3                                      (PRS_ASYNC_BUFC_THR3)
#define PRS_BUFC_CNT0                                      (PRS_ASYNC_BUFC_CNT0)
#define PRS_BUFC_CNT1                                      (PRS_ASYNC_BUFC_CNT1)
#define PRS_BUFC_FULL                                      (PRS_ASYNC_BUFC_FULL)
#define PRS_MODEML_ADVANCE                                 (PRS_ASYNC_MODEML_ADVANCE)
#define PRS_MODEML_ANT0                                    (PRS_ASYNC_MODEML_ANT0)
#define PRS_MODEML_ANT1                                    (PRS_ASYNC_MODEML_ANT1)
#define PRS_MODEML_COHDSADET                               (PRS_ASYNC_MODEML_COHDSADET)
#define PRS_MODEML_COHDSALIVE                              (PRS_ASYNC_MODEML_COHDSALIVE)
#define PRS_MODEML_DCLK                                    (PRS_ASYNC_MODEML_DCLK)
#define PRS_MODEML_DOUT                                    (PRS_ASYNC_MODEML_DOUT)
#define PRS_MODEML_FRAMEDET                                (PRS_ASYNC_MODEML_FRAMEDET)
#define PRS_MODEM_FRAMESENT                                (PRS_ASYNC_MODEM_FRAMESENT)
#define PRS_MODEM_LOWCORR                                  (PRS_ASYNC_MODEM_LOWCORR)
#define PRS_MODEM_LRDSADET                                 (PRS_ASYNC_MODEM_LRDSADET)
#define PRS_MODEM_LRDSALIVE                                (PRS_ASYNC_MODEM_LRDSALIVE)
#define PRS_MODEM_NEWSYMBOL                                (PRS_ASYNC_MODEM_NEWSYMBOL)
#define PRS_MODEM_NEWWND                                   (PRS_ASYNC_MODEM_NEWWND)
#define PRS_MODEM_POSTPONE                                 (PRS_ASYNC_MODEM_POSTPONE)
#define PRS_MODEM_PREDET                                   (PRS_ASYNC_MODEM_PREDET)
#define PRS_MODEMH_PRESENT                                 (PRS_ASYNC_MODEMH_PRESENT)
#define PRS_MODEMH_RSSIJUMP                                (PRS_ASYNC_MODEMH_RSSIJUMP)
#define PRS_MODEMH_SYNCSENT                                (PRS_ASYNC_MODEMH_SYNCSENT)
#define PRS_MODEMH_TIMDET                                  (PRS_ASYNC_MODEMH_TIMDET)
#define PRS_MODEMH_WEAK                                    (PRS_ASYNC_MODEMH_WEAK)
#define PRS_MODEMH_EOF                                     (PRS_ASYNC_MODEMH_EOF)
#define PRS_FRC_DCLK                                       (PRS_ASYNC_FRC_DCLK)
#define PRS_FRC_DOUT                                       (PRS_ASYNC_FRC_DOUT)
#define PRS_PROTIMERL_BOF                                  (PRS_ASYNC_PROTIMERL_BOF)
#define PRS_PROTIMERL_CC0                                  (PRS_ASYNC_PROTIMERL_CC0)
#define PRS_PROTIMERL_CC1                                  (PRS_ASYNC_PROTIMERL_CC1)
#define PRS_PROTIMERL_CC2                                  (PRS_ASYNC_PROTIMERL_CC2)
#define PRS_PROTIMERL_CC3                                  (PRS_ASYNC_PROTIMERL_CC3)
#define PRS_PROTIMERL_CC4                                  (PRS_ASYNC_PROTIMERL_CC4)
#define PRS_PROTIMERL_LBTF                                 (PRS_ASYNC_PROTIMERL_LBTF)
#define PRS_PROTIMERL_LBTR                                 (PRS_ASYNC_PROTIMERL_LBTR)
#define PRS_PROTIMER_LBTS                                  (PRS_ASYNC_PROTIMER_LBTS)
#define PRS_PROTIMER_POF                                   (PRS_ASYNC_PROTIMER_POF)
#define PRS_PROTIMER_T0MATCH                               (PRS_ASYNC_PROTIMER_T0MATCH)
#define PRS_PROTIMER_T0UF                                  (PRS_ASYNC_PROTIMER_T0UF)
#define PRS_PROTIMER_T1MATCH                               (PRS_ASYNC_PROTIMER_T1MATCH)
#define PRS_PROTIMER_T1UF                                  (PRS_ASYNC_PROTIMER_T1UF)
#define PRS_PROTIMER_WOF                                   (PRS_ASYNC_PROTIMER_WOF)
#define PRS_SYNTH_MUX0                                     (PRS_ASYNC_SYNTH_MUX0)
#define PRS_SYNTH_MUX1                                     (PRS_ASYNC_SYNTH_MUX1)
#define PRS_PRSL_ASYNCH0                                   (PRS_ASYNC_PRSL_ASYNCH0)
#define PRS_PRSL_ASYNCH1                                   (PRS_ASYNC_PRSL_ASYNCH1)
#define PRS_PRSL_ASYNCH2                                   (PRS_ASYNC_PRSL_ASYNCH2)
#define PRS_PRSL_ASYNCH3                                   (PRS_ASYNC_PRSL_ASYNCH3)
#define PRS_PRSL_ASYNCH4                                   (PRS_ASYNC_PRSL_ASYNCH4)
#define PRS_PRSL_ASYNCH5                                   (PRS_ASYNC_PRSL_ASYNCH5)
#define PRS_PRSL_ASYNCH6                                   (PRS_ASYNC_PRSL_ASYNCH6)
#define PRS_PRSL_ASYNCH7                                   (PRS_ASYNC_PRSL_ASYNCH7)
#define PRS_PRS_ASYNCH8                                    (PRS_ASYNC_PRS_ASYNCH8)
#define PRS_PRS_ASYNCH9                                    (PRS_ASYNC_PRS_ASYNCH9)
#define PRS_PRS_ASYNCH10                                   (PRS_ASYNC_PRS_ASYNCH10)
#define PRS_PRS_ASYNCH11                                   (PRS_ASYNC_PRS_ASYNCH11)
#define PRS_RACL_ACTIVE                                    (PRS_ASYNC_RACL_ACTIVE)
#define PRS_RACL_LNAEN                                     (PRS_ASYNC_RACL_LNAEN)
#define PRS_RACL_PAEN                                      (PRS_ASYNC_RACL_PAEN)
#define PRS_RACL_RX                                        (PRS_ASYNC_RACL_RX)
#define PRS_RACL_TX                                        (PRS_ASYNC_RACL_TX)
#define PRS_RACL_CTIOUT0                                   (PRS_ASYNC_RACL_CTIOUT0)
#define PRS_RACL_CTIOUT1                                   (PRS_ASYNC_RACL_CTIOUT1)
#define PRS_RACL_CTIOUT2                                   (PRS_ASYNC_RACL_CTIOUT2)
#define PRS_RAC_CTIOUT3                                    (PRS_ASYNC_RAC_CTIOUT3)
#define PRS_RAC_AUXADCDATA                                 (PRS_ASYNC_RAC_AUXADCDATA)
#define PRS_RAC_AUXADCDATAVALID                            (PRS_ASYNC_RAC_AUXADCDATAVALID)
#define PRS_TIMER4_UF                                      (PRS_ASYNC_TIMER4_UF)
#define PRS_TIMER4_OF                                      (PRS_ASYNC_TIMER4_OF)
#define PRS_TIMER4_CC0                                     (PRS_ASYNC_TIMER4_CC0)
#define PRS_TIMER4_CC1                                     (PRS_ASYNC_TIMER4_CC1)
#define PRS_TIMER4_CC2                                     (PRS_ASYNC_TIMER4_CC2)
#define PRS_ACMP0_OUT                                      (PRS_ASYNC_ACMP0_OUT)
#define PRS_ACMP1_OUT                                      (PRS_ASYNC_ACMP1_OUT)
#define PRS_VDAC0L_CH0WARM                                 (PRS_ASYNC_VDAC0L_CH0WARM)
#define PRS_VDAC0L_CH1WARM                                 (PRS_ASYNC_VDAC0L_CH1WARM)
#define PRS_VDAC0L_CH0DONEASYNC                            (PRS_ASYNC_VDAC0L_CH0DONEASYNC)
#define PRS_VDAC0L_CH1DONEASYNC                            (PRS_ASYNC_VDAC0L_CH1DONEASYNC)
#define PRS_VDAC0L_INTERNALTIMEROF                         (PRS_ASYNC_VDAC0L_INTERNALTIMEROF)
#define PRS_VDAC0L_REFRESHTIMEROF                          (PRS_ASYNC_VDAC0L_REFRESHTIMEROF)
#define PRS_PCNT0_DIR                                      (PRS_ASYNC_PCNT0_DIR)
#define PRS_PCNT0_UFOF                                     (PRS_ASYNC_PCNT0_UFOF)
#define PRS_SYSRTC0_GRP0OUT0                               (PRS_ASYNC_SYSRTC0_GRP0OUT0)
#define PRS_SYSRTC0_GRP0OUT1                               (PRS_ASYNC_SYSRTC0_GRP0OUT1)
#define PRS_SYSRTC0_GRP1OUT0                               (PRS_ASYNC_SYSRTC0_GRP1OUT0)
#define PRS_SYSRTC0_GRP1OUT1                               (PRS_ASYNC_SYSRTC0_GRP1OUT1)
#define PRS_LESENSE_DECOUT0                                (PRS_ASYNC_LESENSE_DECOUT0)
#define PRS_LESENSE_DECOUT1                                (PRS_ASYNC_LESENSE_DECOUT1)
#define PRS_LESENSE_DECOUT2                                (PRS_ASYNC_LESENSE_DECOUT2)
#define PRS_LESENSE_DECCMP                                 (PRS_ASYNC_LESENSE_DECCMP)
#define PRS_HFXO0L_STATUS                                  (PRS_ASYNC_HFXO0L_STATUS)
#define PRS_HFXO0L_STATUS1                                 (PRS_ASYNC_HFXO0L_STATUS1)
#define PRS_EUSART0L_CS                                    (PRS_ASYNC_EUSART0L_CS)
#define PRS_EUSART0L_IRDATX                                (PRS_ASYNC_EUSART0L_IRDATX)
#define PRS_EUSART0L_RTS                                   (PRS_ASYNC_EUSART0L_RTS)
#define PRS_EUSART0L_RXDATAV                               (PRS_ASYNC_EUSART0L_RXDATAV)
#define PRS_EUSART0L_TX                                    (PRS_ASYNC_EUSART0L_TX)
#define PRS_EUSART0L_TXC                                   (PRS_ASYNC_EUSART0L_TXC)
#define PRS_EUSART0L_RXFL                                  (PRS_ASYNC_EUSART0L_RXFL)
#define PRS_EUSART0L_TXFL                                  (PRS_ASYNC_EUSART0L_TXFL)
#define PRS_EUSART1L_CS                                    (PRS_ASYNC_EUSART1L_CS)
#define PRS_EUSART1L_IRDATX                                (PRS_ASYNC_EUSART1L_IRDATX)
#define PRS_EUSART1L_RTS                                   (PRS_ASYNC_EUSART1L_RTS)
#define PRS_EUSART1L_RXDATAV                               (PRS_ASYNC_EUSART1L_RXDATAV)
#define PRS_EUSART1L_TX                                    (PRS_ASYNC_EUSART1L_TX)
#define PRS_EUSART1L_TXC                                   (PRS_ASYNC_EUSART1L_TXC)
#define PRS_EUSART1L_RXFL                                  (PRS_ASYNC_EUSART1L_RXFL)
#define PRS_EUSART1L_TXFL                                  (PRS_ASYNC_EUSART1L_TXFL)
#define PRS_EUSART2L_CS                                    (PRS_ASYNC_EUSART2L_CS)
#define PRS_EUSART2L_IRDATX                                (PRS_ASYNC_EUSART2L_IRDATX)
#define PRS_EUSART2L_RTS                                   (PRS_ASYNC_EUSART2L_RTS)
#define PRS_EUSART2L_RXDATAV                               (PRS_ASYNC_EUSART2L_RXDATAV)
#define PRS_EUSART2L_TX                                    (PRS_ASYNC_EUSART2L_TX)
#define PRS_EUSART2L_TXC                                   (PRS_ASYNC_EUSART2L_TXC)
#define PRS_EUSART2L_RXFL                                  (PRS_ASYNC_EUSART2L_RXFL)
#define PRS_EUSART2L_TXFL                                  (PRS_ASYNC_EUSART2L_TXFL)

#endif // EFR32FG23_PRS_SIGNALS_H
