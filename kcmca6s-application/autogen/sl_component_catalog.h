#ifndef SL_COMPONENT_CATALOG_H
#define SL_COMPONENT_CATALOG_H

// APIs present in project
#define SL_CATALOG_APP_ASSERT_PRESENT
#define SL_CATALOG_CLOCK_MANAGER_PRESENT
#define SL_CATALOG_DEVICE_INIT_PRESENT
#define SL_CATALOG_DEVICE_INIT_CORE_PRESENT
#define SL_CATALOG_DEVICE_INIT_DCDC_PRESENT
#define SL_CATALOG_EMLIB_CORE_PRESENT
#define SL_CATALOG_EMLIB_CORE_DEBUG_CONFIG_PRESENT
#define SL_CATALOG_HFXO_MANAGER_PRESENT
#define SL_CATALOG_INTERRUPT_MANAGER_PRESENT
#define SL_CATALOG_IOSTREAM_PRESENT
#define SL_CATALOG_IOSTREAM_EUSART_PRESENT
#define SL_CATALOG_IOSTREAM_UART_COMMON_PRESENT
#define SL_CATALOG_MEMORY_MANAGER_PRESENT
#define SL_CATALOG_MPU_PRESENT
#define SL_CATALOG_NVM3_PRESENT
#define SL_CATALOG_POWER_MANAGER_PRESENT
#define SL_CATALOG_RADIO_CONFIG_WMBUS_COLLECTOR_PRESENT
#define SL_CATALOG_RAIL_LIB_PRESENT
#define SL_CATALOG_RAIL_UTIL_INIT_PRESENT
#define SL_CATALOG_RAIL_UTIL_PTI_PRESENT
#define SL_CATALOG_RAIL_UTIL_RF_PATH_PRESENT
#define SL_CATALOG_SE_MANAGER_PRESENT
#define SL_CATALOG_LED0_PRESENT
#define SL_CATALOG_SIMPLE_LED_PRESENT
#define SL_CATALOG_SIMPLE_LED_LED0_PRESENT
#define SL_CATALOG_SIMPLE_RAIL_ASSISTANCE_PRESENT
#define SL_CATALOG_RAIL_PACKAGE_ASSISTANT_PRESENT
#define SL_CATALOG_FLEX_RAIL_CONFIG_PRESENT
#define SL_CATALOG_SL_SIMPLE_RAIL_STUB_PRESENT
#define SL_CATALOG_WMBUS_SUPPORT_PRESENT
#define SL_CATALOG_SLEEPTIMER_PRESENT

#endif // SL_COMPONENT_CATALOG_H