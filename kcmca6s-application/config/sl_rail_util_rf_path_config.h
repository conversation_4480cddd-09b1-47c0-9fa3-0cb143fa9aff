#ifndef SL_RAIL_UTIL_RF_PATH_CONFIG_H
#define SL_RAIL_UTIL_RF_PATH_CONFIG_H

#include "rail_types.h"

// <<< Use Configuration Wizard in Context Menu >>>

// <h> Chip-internal RF Path Configuration
// <o SL_RAIL_UTIL_RF_PATH_INT_RF_PATH_MODE> RF Path Mode
// <RAIL_ANTENNA_0=> Path 0
// <RAIL_ANTENNA_1=> Path 1
// <RAIL_ANTENNA_AUTO=> Auto
// <i> Default: RAIL_ANTENNA_0
#define SL_RAIL_UTIL_RF_PATH_INT_RF_PATH_MODE  RAIL_ANTENNA_0
// </h>

// <<< end of configuration section >>>

#endif // SL_RAIL_UTIL_RF_PATH_CONFIG_H
