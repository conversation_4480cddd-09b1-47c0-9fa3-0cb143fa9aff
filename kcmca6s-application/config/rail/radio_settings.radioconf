<?xml version="1.0" encoding="UTF-8"?>
<multi_phy_configuration part_family="ocelot" part_revision="A0" rail_adapter_version="rail_api_2.x" status_code="0" xsd_version="0.0.20">
  <base_channel_configurations>
    <base_channel_configuration name="wmbus_mode_t" profile="Mbus">
      <channel_config_entries>
        <channel_config_entry name="m2o_tx">
          <channel_number_start>0</channel_number_start>
          <channel_number_end>0</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeT_M2O_100k_frameA"}</metadata>
          <phy_name_override>PHY_wMbus_ModeT_M2O_100k_frameA</phy_name_override>
          <profile_input_overrides>
            <override>
              <key>syncword_dualsync</key>
              <value>false</value>
            </override>
            <override>
              <key>mbus_postamble_length</key>
              <value>4</value>
            </override>
            <override>
              <key>preamble_length</key>
              <value>42</value>
            </override>
          </profile_input_overrides>
        </channel_config_entry>
        <channel_config_entry name="o2m_tx">
          <channel_number_start>1</channel_number_start>
          <channel_number_end>1</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeT_O2M_32p768k_frameA"}</metadata>
          <phy_name_override>PHY_wMbus_ModeT_O2M_32p768k_frameA</phy_name_override>
          <profile_input_overrides>
            <override>
              <key>preamble_length</key>
              <value>38</value>
            </override>
            <override>
              <key>mbus_postamble_length</key>
              <value>4</value>
            </override>
          </profile_input_overrides>
        </channel_config_entry>
        <channel_config_entry name="m2o_rx">
          <channel_number_start>2</channel_number_start>
          <channel_number_end>2</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeTC_M2O_100k_frameA"}</metadata>
          <phy_name_override>PHY_wMbus_ModeTC_M2O_100k_frameA</phy_name_override>
        </channel_config_entry>
        <channel_config_entry name="o2m_rx">
          <channel_number_start>3</channel_number_start>
          <channel_number_end>3</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeT_O2M_32p768k_frameA"}</metadata>
          <phy_name_override>PHY_wMbus_ModeT_O2M_32p768k_frameA</phy_name_override>
        </channel_config_entry>
        <channel_config_entry name="c_frame_a_m2o">
          <channel_number_start>4</channel_number_start>
          <channel_number_end>4</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeC_M2O_100k_frameA"}</metadata>
          <phy_name_override>PHY_wMbus_ModeC_M2O_100k_frameA</phy_name_override>
          <profile_input_overrides>
            <override>
              <key>mbus_postamble_length</key>
              <value>4</value>
            </override>
          </profile_input_overrides>
        </channel_config_entry>
        <channel_config_entry name="c_frame_b_m2o">
          <channel_number_start>5</channel_number_start>
          <channel_number_end>5</channel_number_end>
          <physical_channel_offset>SAME_AS_FIRST_CHANNEL</physical_channel_offset>
          <max_power>RAIL_TX_POWER_MAX</max_power>
          <metadata>{"selectedPhy":"PHY_wMbus_ModeC_M2O_100k_frameB"}</metadata>
          <phy_name_override>PHY_wMbus_ModeC_M2O_100k_frameB</phy_name_override>
          <profile_input_overrides>
            <override>
              <key>mbus_postamble_length</key>
              <value>4</value>
            </override>
          </profile_input_overrides>
        </channel_config_entry>
      </channel_config_entries>
      <metadata>{"selectedPhy":"PHY_wMbus_ModeTC_M2O_100k_frameA"}</metadata>
      <phy name="PHY_wMbus_ModeTC_M2O_100k_frameA"/>
    </base_channel_configuration>
  </base_channel_configurations>
</multi_phy_configuration>