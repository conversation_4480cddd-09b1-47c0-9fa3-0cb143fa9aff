/***************************************************************************//**
 * @file
 * @brief Interrupt Manager configuration file for series 2 devices.
 *******************************************************************************
 * # License
 * <b>Copyright 2023 Silicon Laboratories Inc. www.silabs.com</b>
 *******************************************************************************
 *
 * SPDX-License-Identifier: Zlib
 *
 * The licensor of this software is Silicon Laboratories Inc.
 *
 * This software is provided 'as-is', without any express or implied
 * warranty. In no event will the authors be held liable for any damages
 * arising from the use of this software.
 *
 * Permission is granted to anyone to use this software for any purpose,
 * including commercial applications, and to alter it and redistribute it
 * freely, subject to the following restrictions:
 *
 * 1. The origin of this software must not be misrepresented; you must not
 *    claim that you wrote the original software. If you use this software
 *    in a product, an acknowledgment in the product documentation would be
 *    appreciated but is not required.
 * 2. Altered source versions must be plainly marked as such, and must not be
 *    misrepresented as being the original software.
 * 3. This notice may not be removed or altered from any source distribution.
 *
 ******************************************************************************/

// <<< Use Configuration Wizard in Context Menu >>>

#ifndef SL_INTERRUPT_MANAGER_S2_CONFIG_H
#define SL_INTERRUPT_MANAGER_S2_CONFIG_H

// <h>Interrupt Manager Configuration

// <q SL_INTERRUPT_MANAGER_S2_INTERRUPTS_IN_RAM> Put the interrupt vector table in RAM.
// <i> Set to 1 to put the vector table in RAM.
// <i> Default: 0
#define SL_INTERRUPT_MANAGER_S2_INTERRUPTS_IN_RAM  0

// </h>

#endif /* SSL_INTERRUPT_MANAGER_S2_CONFIG_H */

// <<< end of configuration section >>>
