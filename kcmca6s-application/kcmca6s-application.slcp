# Silicon Labs Project Configuration Tools: slcp, v0, Component selection file.
project_name: kcmca6s-application
label: kcmca6s-application
description: |
  Implements a Wireless M-Bus collector application. Uses the multi-PHY configurator. For details, see AN1119. With one click, this bare-metal sample app can be run on an OS; currently, MicriumOS and FreeRTOS are supported. NOTE: Due to the higher current consumption of continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed.
category: RAIL Examples
filter:
- name: Device Type
  value: [SoC]
- name: Project Difficulty
  value: [Beginner]
- name: Wireless Technology
  value: [RAIL]
package: Flex
quality: production
readme:
- {path: readme.md}
- {path: readme.md}
source:
- {path: main.c}
- {path: app_init.c}
- {path: app_process.c}
include:
- path: .
  file_list:
  - {path: app_init.h}
  - {path: app_process.h}
sdk: {id: simplicity_sdk, version: 2024.6.2}
toolchain_settings:
- {value: debug, option: optimize}
component:
- {id: EFR32FG23B010F512IM48}
- {id: app_assert}
- {id: board_control}
- {id: brd2600a_a01}
- {id: clock_manager}
- {id: device_init}
- {id: emlib_usart}
- {id: iostream_recommended_stream}
- {id: mpu}
- {id: nvm3_default}
- {id: nvm3_lib}
- {id: power_manager}
- {id: radio_config_wmbus_collector}
- instance: [inst0]
  id: rail_util_init
- {id: rail_util_recommended}
- {id: restrictions_profile_wmbus}
- {id: simple_rail_assistance}
- {id: sl_simple_rail_stub}
- {id: sl_system}
- {id: sl_wmbus_support}
requires:
- {name: a_radio_config}
- {name: sl_flex_rail_config}
- {name: sl_flex_rail_channel_selector}
- {name: simple_led}
configuration:
- {name: SL_STACK_SIZE, value: '4096'}
- {name: configMINIMAL_STACK_SIZE, value: '1024'}
- {name: CPU_CFG_TS_32_EN, value: '1'}
- {name: OS_CFG_DBG_EN, value: '1'}
- {name: OS_CFG_TS_EN, value: '1'}
- {name: OS_CFG_SCHED_ROUND_ROBIN_EN, value: '1'}
- {name: OS_CFG_STAT_TASK_EN, value: '1'}
- {name: OS_CFG_TASK_PROFILE_EN, value: '1'}
- {name: SL_RAIL_UTIL_INIT_INIT_COMPLETE_CALLBACK_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_RADIO_CONFIG_SUPPORT_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_PROTOCOLS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_PROTOCOL_INST0_DEFAULT, value: SL_RAIL_UTIL_PROTOCOL_PROPRIETARY}
- {name: SL_RAIL_UTIL_PROTOCOL_BLE_ENABLE, value: '0'}
- {name: SL_RAIL_UTIL_PROTOCOL_IEEE802154_2P4GHZ_ENABLE, value: '0'}
- {name: SL_RAIL_UTIL_PROTOCOL_IEEE802154_GB868_ENABLE, value: '0'}
- {name: SL_RAIL_UTIL_PROTOCOL_ZWAVE_ENABLE, value: '0'}
- {name: SL_RAIL_UTIL_PROTOCOL_SIDEWALK_ENABLE, value: '0'}
- {name: SL_RAIL_UTIL_INIT_CALIBRATIONS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_CALIBRATION_TEMPERATURE_NOTIFY_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_CALIBRATION_ONETIME_NOTIFY_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_TRANSITIONS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_TRANSITION_INST0_RX_SUCCESS, value: RAIL_RF_STATE_RX}
- {name: SL_RAIL_UTIL_INIT_TRANSITION_INST0_RX_ERROR, value: RAIL_RF_STATE_RX}
- {name: SL_RAIL_UTIL_INIT_DATA_FORMATS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENTS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_PREAMBLE_LOST_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_PACKET_RECEIVED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_ADDRESS_FILTERED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_FRAME_ERROR_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_PACKET_ABORTED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_PACKET_SENT_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_ABORTED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_BLOCKED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_FIFO_OVERFLOW_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_UNDERFLOW_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_CHANNEL_BUSY_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_TX_SCHEDULED_RX_TX_STARTED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_RX_SCHEDULED_RX_MISSED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_TX_SCHEDULED_TX_MISSED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_CONFIG_UNSCHEDULED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_CONFIG_SCHEDULED_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_SCHEDULER_STATUS_INST0_ENABLE, value: '1'}
- {name: SL_RAIL_UTIL_INIT_EVENT_CAL_NEEDED_INST0_ENABLE, value: '1'}
- condition: [device_sdid_220]
  name: SL_RAIL_UTIL_PA_SELECTION_SUBGHZ
  value: RAIL_TX_POWER_MODE_SUBGIG_POWERSETTING_TABLE
- condition: [device_sdid_220]
  name: SL_RAIL_UTIL_PA_SELECTION_OFDM
  value: RAIL_TX_POWER_MODE_OFDM_PA_POWERSETTING_TABLE
- condition: [device_sdid_220, hardware_board_default_rf_band_470]
  name: SL_CLOCK_MANAGER_RFFPLL_BAND
  value: '1'
- condition: [device_sdid_220, hardware_board_default_rf_band_450]
  name: SL_CLOCK_MANAGER_RFFPLL_BAND
  value: '0'
- unless: [device_has_crypto]
  name: SL_MBEDTLS_HARDWARE_ACCELERATION_ENABLED
  value: '0'
- {name: SL_BOARD_ENABLE_VCOM, value: '1'}
- {name: SL_IOSTREAM_USART_VCOM_CONVERT_BY_DEFAULT_LF_TO_CRLF, value: (1)}
- {name: SL_IOSTREAM_USART_VCOM_FLOW_CONTROL_TYPE, value: usartHwFlowControlNone}
- {name: SL_IOSTREAM_EUSART_VCOM_CONVERT_BY_DEFAULT_LF_TO_CRLF, value: (1)}
- {name: SL_IOSTREAM_EUSART_VCOM_FLOW_CONTROL_TYPE, value: eusartHwFlowControlNone}
- {name: APP_LOG_PREFIX_ENABLE, value: '0'}
- condition: [hardware_board_supports_rf_band_868]
  name: SL_WMBUS_TYPE
  value: WMBUS_MODE_T_METER
- condition: [hardware_board_supports_rf_band_434]
  name: SL_WMBUS_TYPE
  value: WMBUS_MODE_F
- condition: [hardware_board_supports_rf_band_169]
  name: SL_WMBUS_TYPE
  value: WMBUS_MODE_N_48
ui_hints:
  highlight:
  - {path: config/rail/radio_settings.radioconf}
  - {path: readme.md, focus: true}

