{"data": [{"hit": 11, "alias": "main.c", "type": "FILE", "data": "C:/Users/<USER>/Desktop/EFR32FG23B_Project/KCMCA6S/kcmca6s-application/main.c"}, {"hit": 8, "alias": "Software Components", "type": "NAVIGATION", "data": "softwarecomponents"}, {"hit": 7, "alias": "app_init.c", "type": "FILE", "data": "C:/Users/<USER>/Desktop/EFR32FG23B_Project/KCMCA6S/kcmca6s-application/app_init.c"}, {"hit": 2, "alias": "app_process.c", "type": "FILE", "data": "C:/Users/<USER>/Desktop/EFR32FG23B_Project/KCMCA6S/kcmca6s-application/app_process.c"}, {"hit": 1, "alias": "NVM3 Default Config", "type": "COMPONENT", "data": "{\"componentId\":\"nvm3_default_config\"}"}, {"hit": 0, "alias": "readme.md", "type": "FILE", "data": "C:/Users/<USER>/Desktop/EFR32FG23B_Project/KCMCA6S/kcmca6s-application/readme.md"}, {"hit": 0, "alias": "Post Build Editor", "type": "TOOL", "data": "postbuildeditor"}, {"hit": 0, "alias": "<PERSON><PERSON>", "type": "TOOL", "data": "pintool"}, {"hit": 0, "alias": "Proprietary Radio Configurator", "type": "TOOL", "data": "multiPhyRadioConfigurator"}, {"hit": 0, "alias": "Memory Editor", "type": "TOOL", "data": "memoryeditor"}, {"hit": 0, "alias": "docs.silabs.com", "type": "EXTERNAL_URL", "data": "https://docs.silabs.com/"}, {"hit": 0, "alias": "IO Stream: EUSART (vcom)", "type": "COMPONENT", "data": "{\"componentId\":\"iostream_eusart\",\"instanceName\":\"vcom\"}"}]}