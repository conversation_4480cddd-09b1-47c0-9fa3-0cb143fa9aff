<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" moduleId="org.eclipse.cdt.core.settings" name="GNU ARM v12.2.1 - Default">
				<macros>
					<stringMacro name="StudioSdkPath" type="VALUE_PATH_DIR" value="${StudioSdkPathFromID:com.silabs.sdk.stack.sisdk:2024.6.2._901395569}"/>
					<stringMacro name="StudioToolchainPath" type="VALUE_PATH_DIR" value="${StudioToolchainPathFromID:com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205}"/>
				</macros>
				<externalSettings/>
				<extensions>
					<extension id="com.silabs.ss.framework.debugger.core.HEX" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.EBL" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.GBL" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.BIN" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.S37" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule buildConfig.needsApplyStock="true" buildConfig.stockConfigId="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" cppBuildConfig.projectBuiltInState="[{&quot;builtinMacrosMap&quot;:{&quot;HFXO_FREQ&quot;:&quot;39000000&quot;,&quot;SL_RAIL_UTIL_PA_CONFIG_HEADER&quot;:&quot;&lt;sl_rail_util_pa_config.h&gt;&quot;,&quot;CMSIS_NVIC_VIRTUAL&quot;:&quot;1&quot;,&quot;SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT&quot;:&quot;0&quot;,&quot;SL_CODE_COMPONENT_SLEEPTIMER&quot;:&quot;sleeptimer&quot;,&quot;HARDWARE_BOARD_DEFAULT_RF_BAND_868&quot;:&quot;1&quot;,&quot;HARDWARE_BOARD_SUPPORTS_RF_BAND_915&quot;:&quot;1&quot;,&quot;SL_BOARD_REV&quot;:&quot;\&quot;A01\&quot;&quot;,&quot;HARDWARE_BOARD_SUPPORTS_RF_BAND_868&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_POWER_MANAGER&quot;:&quot;power_manager&quot;,&quot;CMSIS_NVIC_VIRTUAL_HEADER_FILE&quot;:&quot;\&quot;cmsis_nvic_virtual.h\&quot;&quot;,&quot;MBEDTLS_PSA_CRYPTO_CLIENT&quot;:&quot;1&quot;,&quot;SL_BOARD_NAME&quot;:&quot;\&quot;BRD2600A\&quot;&quot;,&quot;SL_CLOCK_MANAGER_AUTO_BAND_VALID&quot;:&quot;1&quot;,&quot;MBEDTLS_PSA_CRYPTO_CONFIG_FILE&quot;:&quot;&lt;psa_crypto_config.h&gt;&quot;,&quot;SL_CODE_COMPONENT_CORE&quot;:&quot;core&quot;,&quot;EFR32FG23B010F512IM48&quot;:&quot;1&quot;,&quot;HARDWARE_BOARD_SUPPORTS_2_RF_BANDS&quot;:&quot;1&quot;,&quot;MBEDTLS_CONFIG_FILE&quot;:&quot;&lt;sl_mbedtls_config.h&gt;&quot;,&quot;SL_MEMORY_POOL_LIGHT&quot;:&quot;1&quot;,&quot;SL_COMPONENT_CATALOG_PRESENT&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_PERIPHERAL_SYSRTC&quot;:&quot;hal_sysrtc&quot;},&quot;builtinLibraryPathsStr&quot;:&quot;&quot;,&quot;builtinLibraryFilesStr&quot;:&quot;&quot;,&quot;builtinLibraryNames&quot;:[&quot;gcc&quot;,&quot;c&quot;,&quot;m&quot;,&quot;nosys&quot;],&quot;builtinLibraryObjectsStr&quot;:&quot;&quot;,&quot;id&quot;:&quot;&quot;,&quot;builtinIncludesStr&quot;:&quot;studio:/project/autogen/ studio:/project/config/ studio:/project/config/rail/ studio:/project/ studio:/project/simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/ studio:/project/simplicity_sdk_2024.6.2/app/common/util/app_assert/ studio:/project/simplicity_sdk_2024.6.2/platform/common/inc/ studio:/project/simplicity_sdk_2024.6.2/hardware/board/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/src/ studio:/project/simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/ studio:/project/simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/driver/debug/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/device_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/device_init/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/common/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emlib/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/peripheral/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm/ studio:/project/simplicity_sdk_2024.6.2/platform/service/iostream/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/preset/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/ studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/src/ studio:/project/simplicity_sdk_2024.6.2/platform/service/mpu/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/power_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/ studio:/project/simplicity_sdk_2024.6.2/platform/common/toolchain/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/system/inc/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/ studio:/project/simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/udelay/inc/ studio:/project/autogen/ studio:/project/config/ studio:/project/config/rail/ studio:/project/ studio:/project/simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/ studio:/project/simplicity_sdk_2024.6.2/app/common/util/app_assert/ studio:/project/simplicity_sdk_2024.6.2/platform/common/inc/ studio:/project/simplicity_sdk_2024.6.2/hardware/board/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/src/ studio:/project/simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/ studio:/project/simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/driver/debug/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/device_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/device_init/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/common/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emlib/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/peripheral/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm/ studio:/project/simplicity_sdk_2024.6.2/platform/service/iostream/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/preset/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/ studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/src/ studio:/project/simplicity_sdk_2024.6.2/platform/service/mpu/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/power_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/ studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/ studio:/project/simplicity_sdk_2024.6.2/platform/common/toolchain/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/system/inc/ studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/ studio:/project/simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/ studio:/project/simplicity_sdk_2024.6.2/platform/service/udelay/inc/&quot;,&quot;resolvedOptionsStr&quot;:&quot;[{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}\&quot;],\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;gnu.c.link.option.userobjs\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[\&quot;-mcmse\&quot;,\&quot;--specs=nano.specs\&quot;,\&quot;-fmessage-length=0\&quot;,\&quot;-c\&quot;],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.cpp.compiler.option.misc.otherlist\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-mcmse\&quot;:\&quot;TRUE\&quot;,\&quot;--specs=nano.specs\&quot;:\&quot;TRUE\&quot;,\&quot;-fmessage-length=0\&quot;:\&quot;TRUE\&quot;,\&quot;-c\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.pedantic\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.allwarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[\&quot;-mcmse\&quot;,\&quot;--specs=nano.specs\&quot;,\&quot;-fmessage-length=0\&quot;,\&quot;-c\&quot;],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.c.compiler.option.misc.otherlist\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-mcmse\&quot;:\&quot;TRUE\&quot;,\&quot;--specs=nano.specs\&quot;:\&quot;TRUE\&quot;,\&quot;-fmessage-length=0\&quot;:\&quot;TRUE\&quot;,\&quot;-c\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.toerrors\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.dependencies.projects\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.allwarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.extrawarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.cpp17\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[\&quot;sl_gcc_preinclude.h\&quot;],\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.preinclude\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;sl_gcc_preinclude.h\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.pedantic\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[\&quot;sl_gcc_preinclude.h\&quot;],\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.preinclude\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;sl_gcc_preinclude.h\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base\&quot;,\&quot;listValues\&quot;:[\&quot;sl_gcc_preinclude.h\&quot;],\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.preinclude\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;sl_gcc_preinclude.h\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script\&quot;,\&quot;value\&quot;:\&quot;${workspace_loc:/${ProjName}/autogen/linkerfile.ld}\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.extrawarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.dependencies.projects\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level.debug\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.dependencies.projects\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.toerrors\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}\&quot;],\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;gnu.cpp.link.option.userobjs\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect\&quot;,\&quot;value\&quot;:\&quot;gnu.c.compiler.dialect.c18\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;listValues\&quot;:[\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.c.link.option.ldflags\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;:\&quot;TRUE\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;listValues\&quot;:[\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.cpp.link.option.flags\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;:\&quot;TRUE\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;:\&quot;TRUE\&quot;}},{\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;listValues\&quot;:[],\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level.debug\&quot;,\&quot;listValuesMap\&quot;:{}}]&quot;}]" moduleId="com.silabs.ss.framework.ide.project.core.cpp" projectCommon.boardIds="brd2600a:0.0.0.A01" projectCommon.buildArtifactType="EXE" projectCommon.partId="mcu.arm.efr32.fg23.efr32fg23b010f512im48" projectCommon.referencedModules="[{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.CommonProjectPostBuild.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ProjectPostBuild.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/radioconf_generation_log.json&quot;,&quot;autogen/rail_config.c&quot;,&quot;autogen/rail_config.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.apack_radioConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;config/app_assert_config.h&quot;,&quot;config/dmadrv_config.h&quot;,&quot;config/emlib_core_debug_config.h&quot;,&quot;config/nvm3_default_config.h&quot;,&quot;config/psa_crypto_config.h&quot;,&quot;config/rail/profile_wmbus.restriction&quot;,&quot;config/rail/radio_settings.radioconf&quot;,&quot;config/sl_board_control_config.h&quot;,&quot;config/sl_clock_manager_oscillator_config.h&quot;,&quot;config/sl_clock_manager_tree_config.h&quot;,&quot;config/sl_core_config.h&quot;,&quot;config/sl_debug_swo_config.h&quot;,&quot;config/sl_device_init_dcdc_config.h&quot;,&quot;config/sl_flex_rail_channel_selector_config.h&quot;,&quot;config/sl_flex_rail_config.h&quot;,&quot;config/sl_hfxo_manager_config.h&quot;,&quot;config/sl_interrupt_manager_s2_config.h&quot;,&quot;config/sl_iostream_eusart_vcom_config.h&quot;,&quot;config/sl_mbedtls_config.h&quot;,&quot;config/sl_mbedtls_device_config.h&quot;,&quot;config/sl_memory_config.h&quot;,&quot;config/sl_memory_manager_config.h&quot;,&quot;config/sl_memory_manager_region_config.h&quot;,&quot;config/sl_power_manager_config.h&quot;,&quot;config/sl_rail_util_callbacks_config.h&quot;,&quot;config/sl_rail_util_init_inst0_config.h&quot;,&quot;config/sl_rail_util_pa_config.h&quot;,&quot;config/sl_rail_util_power_manager_init_config.h&quot;,&quot;config/sl_rail_util_protocol_config.h&quot;,&quot;config/sl_rail_util_pti_config.h&quot;,&quot;config/sl_rail_util_rssi_config.h&quot;,&quot;config/sl_simple_led_led0_config.h&quot;,&quot;config/sl_sleeptimer_config.h&quot;,&quot;config/sl_wmbus_support_config.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/RTE_Components.h&quot;,&quot;autogen/linkerfile.ld&quot;,&quot;autogen/sl_board_default_init.c&quot;,&quot;autogen/sl_component_catalog.h&quot;,&quot;autogen/sl_event_handler.c&quot;,&quot;autogen/sl_event_handler.h&quot;,&quot;autogen/sl_iostream_handles.c&quot;,&quot;autogen/sl_iostream_handles.h&quot;,&quot;autogen/sl_iostream_init_eusart_instances.c&quot;,&quot;autogen/sl_iostream_init_eusart_instances.h&quot;,&quot;autogen/sl_iostream_init_instances.h&quot;,&quot;autogen/sl_power_manager_handler.c&quot;,&quot;autogen/sl_rail_util_callbacks.c&quot;,&quot;autogen/sl_rail_util_init.c&quot;,&quot;autogen/sl_rail_util_init.h&quot;,&quot;autogen/sl_simple_led_instances.c&quot;,&quot;autogen/sl_simple_led_instances.h&quot;,&quot;autogen/sli_mbedtls_config_autogen.h&quot;,&quot;autogen/sli_mbedtls_config_transform_autogen.h&quot;,&quot;autogen/sli_psa_builtin_config_autogen.h&quot;,&quot;autogen/sli_psa_config_autogen.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucTemplate.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;app_init.c&quot;,&quot;app_init.h&quot;,&quot;app_process.c&quot;,&quot;app_process.h&quot;,&quot;main.c&quot;,&quot;readme.md&quot;,&quot;simplicity_sdk_2024.6.2/app/common/util/app_assert/app_assert.h&quot;,&quot;simplicity_sdk_2024.6.2/app/common/util/app_assert/sl_app_assert.h&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.c&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.h&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.c&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.h&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.c&quot;,&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.h&quot;,&quot;simplicity_sdk_2024.6.2/hardware/board/inc/sl_board_control.h&quot;,&quot;simplicity_sdk_2024.6.2/hardware/board/inc/sl_board_init.h&quot;,&quot;simplicity_sdk_2024.6.2/hardware/board/src/sl_board_control_gpio.c&quot;,&quot;simplicity_sdk_2024.6.2/hardware/board/src/sl_board_init.c&quot;,&quot;simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc/sl_cos.h&quot;,&quot;simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/src/sl_cos.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_compiler.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_gcc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_version.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/core_cm33.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/mpu_armv8.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/tz_context.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_acmp.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_aes.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_buram.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_burtc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_cmu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dcdc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_devinfo.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dma_descriptor.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dpll.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_emu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_eusart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_fsrco.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpcrc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio_port.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfrco.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfxo.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_i2c.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_iadc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_icache.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_keyscan.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcd.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcdrf.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldma.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar_defines.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lesense.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_letimer.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfrco.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfxo.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mailbox.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mpahbram.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_msc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pcnt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pfmxpprf.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs_signals.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_scratchpad.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_semailbox.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_smu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_syscfg.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_sysrtc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_timer.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ulfrco.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_usart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_vdac.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_wdog.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23b010f512im48.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/em_device.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/system_efr32fg23.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Source/startup_efr32fg23.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Source/system_efr32fg23.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_assert.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_atomic.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_bit.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_code_classification.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_common.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_core.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_enum.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_slist.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_status.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_string.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/inc/sli_code_classification.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_assert.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_core_cortexm.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_slist.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_string.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_syscalls.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_gcc_preinclude.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_memory.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_memory_region.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/debug/inc/sl_debug_swo.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/debug/src/sl_debug_swo.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/sl_led.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/sl_simple_led.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/src/sl_led.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/src/sl_simple_led.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/common/inc/ecode.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/dmadrv.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals/dmadrv_signals.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/src/dmadrv.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3_default.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3_generic.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3_hal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3_hal_flash.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc/nvm3_lock.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/src/nvm3_default_common_linker.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/src/nvm3_hal_flash.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emdrv/nvm3/src/nvm3_lock.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_assert.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_burtc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_bus.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_chip.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_cmu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_cmu_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_common.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_core.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_core_generic.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_emu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_eusart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_eusart_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_gpcrc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_gpio.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_ldma.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_msc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_msc_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_prs.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_ramfunc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_se.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_syscfg.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_system.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_system_generic.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_timer.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_usart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_version.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/sli_em_cmu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_burtc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_cmu.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_core.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_emu.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_eusart.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_gpcrc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_gpio.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_ldma.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_msc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_prs.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_se.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_system.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_timer.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_usart.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/peripheral_sysrtc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/peripheral_sysrtc_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/sl_hal_sysrtc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/sl_hal_sysrtc_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/peripheral/src/sl_hal_sysrtc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x/rail_chip_specific.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_assert_error_codes.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_features.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_mfm.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_types.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_10dbm_434M.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_14dbm.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_20dbm.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curve_types_efr32.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/sl_rail_util_callbacks.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/sli_rail_util_callbacks.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol_types.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble/rail_ble.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154/rail_ieee802154.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk/rail_sidewalk.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus/rail_wmbus.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave/rail_zwave.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_attestation.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_check_config.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_cipher.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_config.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_defines.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_entropy.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_hash.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_internal_keys.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_key_derivation.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_key_handling.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_signature.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_types.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_util.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_features.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_mailbox.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_attestation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_cipher.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_entropy.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_hash.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_key_derivation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_key_handling.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_signature.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_util.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_mailbox.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_osal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_osal_baremetal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_acceleration.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_omnipresent.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_psa_acceleration.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/aes_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ccm_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/cmac_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ecjpake_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/gcm_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/se_management.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha1_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha256_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha512_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sl_mbedtls.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_values.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/src/se_aes.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/src/sl_mbedtls.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_common.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_features.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_aead.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_cipher.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_derivation.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_management.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_mac.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_functions.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_types.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_functions.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_types.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_version_dependencies.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_common.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_init.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_aead.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_builtin_keys.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_cipher.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_derivation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_management.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_mac.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_signature.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_aead.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_cipher.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_mac.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_key_derivation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_aead.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_cipher.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_hash.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_mac.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_key_derivation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_version_dependencies.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sl_clock_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sl_clock_manager_init.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sli_clock_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_hal_s2.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_init.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_init_hal_s2.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sli_clock_manager_hal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sli_clock_manager_init_hal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_init/inc/sl_device_init_dcdc.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_init/src/sl_device_init_dcdc_s2.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/clocks/sl_device_clock_efr32xg23.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/devices/sl_device_peripheral_hal_efr32xg23.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/inc/sl_device_clock.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/inc/sl_device_peripheral.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/src/sl_device_clock.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/src/sl_device_peripheral.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/sl_hfxo_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/sli_hfxo_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sl_hfxo_manager.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sl_hfxo_manager_hal_s2.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sli_hfxo_manager_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm/cmsis_nvic_virtual.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/sl_interrupt_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/src/sl_interrupt_manager_cortexm.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/src/sli_interrupt_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream_eusart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream_uart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sli_iostream_uart.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream_eusart.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream_uart.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/sl_memory_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/sl_memory_manager_region.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_dynamic_reservation.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_pool.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_region.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_retarget.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sli_memory_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sli_memory_manager_common.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/mpu/inc/sl_mpu.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/mpu/src/sl_mpu.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sl_power_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sl_power_manager_debug.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sli_power_manager.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager_debug.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager_hal_s2.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sli_power_manager_private.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/sli_sleeptimer.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_burtc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_sysrtc.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_timer.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sli_sleeptimer_hal.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/system/inc/sl_system_init.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/system/inc/sl_system_process_action.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/system/src/sl_system_init.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/system/src/sl_system_process_action.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/udelay/inc/sl_udelay.h&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/udelay/src/sl_udelay.c&quot;,&quot;simplicity_sdk_2024.6.2/platform/service/udelay/src/sl_udelay_armv6m_gcc.S&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/aes.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/aria.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/asn1.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/asn1write.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/base64.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/bignum.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/build_info.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/camellia.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ccm.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/chacha20.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/chachapoly.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/check_config.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/cipher.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/cmac.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/compat-2.x.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_from_psa.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_ssl.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_x509.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_psa.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/constant_time.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ctr_drbg.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/debug.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/des.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/dhm.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecdh.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecdsa.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecjpake.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecp.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/entropy.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/error.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/gcm.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/hkdf.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/hmac_drbg.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/lms.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/mbedtls_config.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/md.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/md5.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/memory_buffer_alloc.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/net_sockets.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/nist_kw.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/oid.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pem.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pk.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs12.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs5.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs7.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform_time.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform_util.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/poly1305.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/private_access.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/psa_util.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ripemd160.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/rsa.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha1.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha256.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha3.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha512.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_cache.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_ciphersuites.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_cookie.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_ticket.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/threading.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/timing.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/version.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_crl.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_crt.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_csr.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/build_info.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_auto_enabled.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_config_synonyms.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_composites.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_key_derivation.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_primitives.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_compat.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_config.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_common.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_composites.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_primitives.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_extra.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_legacy.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_platform.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_se_driver.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_sizes.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_struct.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_types.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_values.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/aes.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/alignment.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/base64_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_core.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod_raw.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod_raw_invasive.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bn_mul.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/cipher_wrap.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/common.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time_impl.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ecp_internal_alt.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ecp_invasive.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/entropy_poll.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/lmots.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/md_psa.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/md_wrap.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_common.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_error.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_reader.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_trace.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/padlock.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pk_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pk_wrap.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pkwrite.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/platform.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/platform_util.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_crypto_client.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_util.c&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_util_internal.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/rsa_alt_helpers.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_client.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_debug_helpers.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_misc.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_tls13_invasive.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_tls13_keys.h&quot;,&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/threading.c&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.componentSetup.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.defaultSettings.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/.crc_config.crc&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucProject.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true}]" projectCommon.savedStockVariables="{&quot;copiedSdkLocation&quot;:&quot;simplicity_sdk_2024.6.2&quot;,&quot;partOpn&quot;:&quot;efr32fg23b010f512im48&quot;}" projectCommon.sdkId="com.silabs.sdk.stack.sisdk:2024.6.2._901395569" projectCommon.toolchainId="com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205"/>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" description="" id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" name="GNU ARM v12.2.1 - Default" parent="com.silabs.ide.si32.gcc.cdt.managedbuild.config.gnu.exe">
					<folderInfo id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205." name="/" resourcePath="">
						<toolChain id="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe.1955851846" name="Si32 GNU ARM" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe">
							<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script.924405014" name="Linker Script:" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script" value="${workspace_loc:/${ProjName}/autogen/linkerfile.ld}" valueType="string"/>
							<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript.1491715424" name="Use custom linker script" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript" value="true" valueType="boolean"/>
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF;org.eclipse.cdt.core.GNU_ELF;com.silabs.ss.framework.debugger.core.BIN;com.silabs.ss.framework.debugger.core.HEX;com.silabs.ss.framework.debugger.core.S37;com.silabs.ss.framework.debugger.core.EBL;com.silabs.ss.framework.debugger.core.GBL" id="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.platform.base.1095909596" isAbstract="false" name="Debug Platform" osList="win32,linux,macosx" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.platform.base"/>
							<builder buildPath="${workspace_loc:/kcmca6s-application}/GNU ARM v12.2.1 - Default" id="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.builder.base.180579273" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Si32 GNU ARM Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base.**********" name="GNU ARM C Compiler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type.1810626446" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable.1118910746" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.preinclude.1503795773" name="Preinclude macros (-imacros)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.preinclude" valueType="stringList">
									<listOptionValue builtIn="false" value="sl_gcc_preinclude.h"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect.2110709511" name="C Language Dialect" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect" value="gnu.c.compiler.dialect.c18" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level.1546865990" name="Optimization Level" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level.debug" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect.1735898973" name="Place each data item into its own section (-fdata-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects.1197231307" name="Place each function into its own section (-ffunction-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer.540633147" name="Omit frame pointer (-fomit-frame-pointer)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.allwarn.2018232163" name="All warnings (-Wall)" superClass="gnu.c.compiler.option.warnings.allwarn" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.extrawarn.1287120504" name="Extra warnings (-Wextra)" superClass="gnu.c.compiler.option.warnings.extrawarn" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.toerrors.478405787" name="Warnings as errors (-Werror)" superClass="gnu.c.compiler.option.warnings.toerrors" value="false" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.pedantic.1426989952" name="Pedantic (-pedantic)" superClass="gnu.c.compiler.option.warnings.pedantic" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.c.compiler.option.misc.otherlist.1399383669" name="Other flags" superClass="com.silabs.gnu.c.compiler.option.misc.otherlist" valueType="stringList">
									<listOptionValue builtIn="false" value="-mcmse"/>
									<listOptionValue builtIn="false" value="--specs=nano.specs"/>
									<listOptionValue builtIn="false" value="-c"/>
									<listOptionValue builtIn="false" value="-fmessage-length=0"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.compiler.option.include.paths.1802030718" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel/ql_common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel/ql_driver/bsp_uart/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel/ql_utils/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel/ql_driver/bsp_nvm/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel/ql_main/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/quectel}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/autogen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config/rail}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/common/util/app_assert}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/hardware/board/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/clock_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/clock_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/driver/debug/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/device_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/device_init/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emlib/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/peripheral/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/iostream/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/driver/leddrv/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/preset}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/util/third_party/mbedtls/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/util/third_party/mbedtls/library}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/memory_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/memory_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/mpu/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/power_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/common/toolchain/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/system/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/udelay/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/kcmca6s-application/quectel/ql_driver/bsp_timer/inc}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.def.symbols.1427509025" name="Defined symbols (-D)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="EFR32FG23B010F512IM48=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_DEFAULT_RF_BAND_868=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_2_RF_BANDS=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_RF_BAND_868=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_RF_BAND_915=1"/>
									<listOptionValue builtIn="false" value="HFXO_FREQ=39000000"/>
									<listOptionValue builtIn="false" value="SL_BOARD_NAME=&quot;BRD2600A&quot;"/>
									<listOptionValue builtIn="false" value="SL_BOARD_REV=&quot;A01&quot;"/>
									<listOptionValue builtIn="false" value="SL_CLOCK_MANAGER_AUTO_BAND_VALID=1"/>
									<listOptionValue builtIn="false" value="SL_COMPONENT_CATALOG_PRESENT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_PERIPHERAL_SYSRTC=hal_sysrtc"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL=1"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL_HEADER_FILE=&quot;cmsis_nvic_virtual.h&quot;"/>
									<listOptionValue builtIn="false" value="MBEDTLS_CONFIG_FILE=&lt;sl_mbedtls_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_MEMORY_POOL_LIGHT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_POWER_MANAGER=power_manager"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CLIENT=1"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CONFIG_FILE=&lt;psa_crypto_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT=0"/>
									<listOptionValue builtIn="false" value="SL_RAIL_UTIL_PA_CONFIG_HEADER=&lt;sl_rail_util_pa_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CORE=core"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLEEPTIMER=sleeptimer"/>
								</option>
								<inputType id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input.715928078" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base.2043312716" name="GNU ARM C++ Compiler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type.999806851" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable.857807040" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.preinclude.1467672177" name="Preinclude macros (-imacros)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.preinclude" valueType="stringList">
									<listOptionValue builtIn="false" value="sl_gcc_preinclude.h"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.1127588139" name="C++ Language Dialect" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.cpp17" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti.487937203" name="No RTTI (-fno-rtti)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions.1768579905" name="No Exceptions (-fno-exceptions)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level.1241918055" name="Optimization Level" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level.debug" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect.1587569540" name="Place each data item into its own section (-fdata-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects.2081136679" name="Place each function into its own section (-ffunction-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer.1964283890" name="Omit frame pointer (-fomit-frame-pointer)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.allwarn.2050756093" name="All warnings (-Wall)" superClass="gnu.cpp.compiler.option.warnings.allwarn" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.extrawarn.695455094" name="Extra warnings (-Wextra)" superClass="gnu.cpp.compiler.option.warnings.extrawarn" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.toerrors.409601930" name="Warnings as errors (-Werror)" superClass="gnu.cpp.compiler.option.warnings.toerrors" value="false" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.pedantic.48031878" name="Pedantic (-pedantic)" superClass="gnu.cpp.compiler.option.warnings.pedantic" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.cpp.compiler.option.misc.otherlist.1047221859" name="Other flags" superClass="com.silabs.gnu.cpp.compiler.option.misc.otherlist" valueType="stringList">
									<listOptionValue builtIn="false" value="-mcmse"/>
									<listOptionValue builtIn="false" value="--specs=nano.specs"/>
									<listOptionValue builtIn="false" value="-c"/>
									<listOptionValue builtIn="false" value="-fmessage-length=0"/>
								</option>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base.94361582" name="GNU ARM Assembler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type.1496532206" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable.741684219" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.preinclude.957658061" name="Preinclude macros (-imacros)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.preinclude" valueType="stringList">
									<listOptionValue builtIn="false" value="sl_gcc_preinclude.h"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.includes.1588079178" name="Include paths (-I)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/autogen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config/rail}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/common/util/app_assert}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/hardware/board/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/clock_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/clock_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/driver/debug/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/device_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/device_init/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emlib/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/peripheral/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/iostream/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/driver/leddrv/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/preset}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/util/third_party/mbedtls/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/util/third_party/mbedtls/library}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/memory_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/memory_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/mpu/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/power_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/common/toolchain/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/system/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/service/udelay/inc}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.as.def.symbols.1004061903" name="Defined symbols (-D)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.as.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="EFR32FG23B010F512IM48=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_DEFAULT_RF_BAND_868=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_2_RF_BANDS=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_RF_BAND_868=1"/>
									<listOptionValue builtIn="false" value="HARDWARE_BOARD_SUPPORTS_RF_BAND_915=1"/>
									<listOptionValue builtIn="false" value="HFXO_FREQ=39000000"/>
									<listOptionValue builtIn="false" value="SL_BOARD_NAME=&quot;BRD2600A&quot;"/>
									<listOptionValue builtIn="false" value="SL_BOARD_REV=&quot;A01&quot;"/>
									<listOptionValue builtIn="false" value="SL_CLOCK_MANAGER_AUTO_BAND_VALID=1"/>
									<listOptionValue builtIn="false" value="SL_COMPONENT_CATALOG_PRESENT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_PERIPHERAL_SYSRTC=hal_sysrtc"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL=1"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL_HEADER_FILE=&quot;cmsis_nvic_virtual.h&quot;"/>
									<listOptionValue builtIn="false" value="MBEDTLS_CONFIG_FILE=&lt;sl_mbedtls_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_MEMORY_POOL_LIGHT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_POWER_MANAGER=power_manager"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CLIENT=1"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CONFIG_FILE=&lt;psa_crypto_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT=0"/>
									<listOptionValue builtIn="false" value="SL_RAIL_UTIL_PA_CONFIG_HEADER=&lt;sl_rail_util_pa_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CORE=core"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLEEPTIMER=sleeptimer"/>
								</option>
								<inputType id="org.eclipse.cdt.core.asmSource.257193939" superClass="org.eclipse.cdt.core.asmSource"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base.1053348783" name="GNU ARM C Linker" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.dependencies.projects.261954188" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.dependencies.projects"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.link.option.userobjs.632547855" name="Other objects" superClass="gnu.c.link.option.userobjs" valueType="userObjs">
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type.1901311654" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable.1967469750" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs.591320307" name="C Library" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map.803884227" name="Generate map file (-Xlinker -Map=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.c.link.option.ldflags.1531341750" name="Linker flags" superClass="com.silabs.gnu.c.link.option.ldflags" valueType="stringList">
									<listOptionValue builtIn="false" value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
									<listOptionValue builtIn="false" value="-Wl,--no-warn-rwx-segments"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency.938292003" name="Use library file circular dependency" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs.1634039629" name="No startup or default libs (-nostdlib)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.link.option.libs.826359872" name="Libraries (-l)" superClass="gnu.c.link.option.libs" valueType="libs">
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="gcc"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="c"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="m"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="nosys"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.170719055" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base.1653894129" name="GNU ARM C++ Linker" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.dependencies.projects.646005595" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.dependencies.projects"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.cpp.link.option.userobjs.569995693" name="Other objects" superClass="gnu.cpp.link.option.userobjs" valueType="userObjs">
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a}"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type.1788415959" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable.1165345114" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs.726756028" name="C Library" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map.1280586681" name="Generate map file (-Xlinker -Map=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.cpp.link.option.flags.929658850" name="Linker flags" superClass="com.silabs.gnu.cpp.link.option.flags" valueType="stringList">
									<listOptionValue builtIn="false" value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
									<listOptionValue builtIn="false" value="-Wl,--no-warn-rwx-segments"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency.691627887" name="Use library file circular dependency" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency" value="true" valueType="boolean"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.base.178118101" name="GNU ARM Archiver" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.dependencies.projects.1102071662" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.dependencies.projects"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="trashed_modified_files|kcmca6s-application_cmake" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="com.silabs.ss.framework.ide.project.core.cpp" project.generation="40" projectCommon.boardIds="brd2600a:0.0.0.A01" projectCommon.buildArtifactType="EXE" projectCommon.copiedFiles="[{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-399450931},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_client.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1598946094},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc/sl_cos.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1895169865},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:949647767},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1434299581},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1760850577},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:630225530},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:799558949},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfxo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1287298888},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_code_classification.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1171978255},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/debug/inc/sl_debug_swo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:397504876},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_primitives.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1298082333},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/src/sl_interrupt_manager_cortexm.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1239574158},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-232327425},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_bus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1305471864},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_hash.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-30157891},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1723601326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/board/src/sl_board_control_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:852554784},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ripemd160.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1380832882},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:364710820},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_iostream_init_eusart_instances.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-298947040},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/app_assert_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:154348492},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/emlib_core_debug_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1542953947},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/peripheral/src/sl_hal_sysrtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1540181629},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_iostream_init_eusart_instances.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1001100391},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pk.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2063714410},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_system_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-234915305},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1996274065},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_mailbox.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2001254255},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/system_efr32fg23.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:677122741},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha256.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-739345185},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha1.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2114221744},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/aes_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1092892438},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_simple_led_led0_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2090181110},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sli_power_manager_private.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1973743488},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1581892898},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/sli_em_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:669920800},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_hash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2075106676},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_retarget.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1758395558},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_eusart.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1275880347},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_letimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1725823837},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_key_handling.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1726731409},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave/rail_zwave.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-231002802},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1847224350},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_rssi_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2000993975},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/inc/sl_device_peripheral.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-500886644},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_from_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1727931491},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/base64.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:199759892},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_entropy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:319364281},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_crt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1365587155},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:890919415},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_eusart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1879360747},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpcrc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1378012793},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:52473552},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_assert.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-583876453},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfxo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-4665177},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_memory_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1690159560},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1150470593},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_init/src/sl_device_init_dcdc_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:627925385},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk/rail_sidewalk.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1001320151},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/private_access.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1917024545},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_cmu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-812517002},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/bignum.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1569302735},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:303415812},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pcnt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1136307861},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_acceleration.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-704582530},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1133355727},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1919675718},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/build_info.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:53228422},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/linkerfile.ld&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1662151654},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/hmac_drbg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:555271352},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_burtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:584876533},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/udelay/src/sl_udelay.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:48187334},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod_raw_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1446891481},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/threading.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-214812895},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/error.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:372423391},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dpll.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-834265598},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/common/util/app_assert/sl_app_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1049380099},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-620961678},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/md.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:984937536},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_region.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-676973296},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_mbedtls_device_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1613143730},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:649994997},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_mailbox.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2002444105},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/system/inc/sl_system_process_action.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:488651171},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154/rail_ieee802154.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-952151142},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_clock_manager_tree_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1625979717},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_component_catalog.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:423492870},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/peripheral_sysrtc_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-758466069},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/sl_memory_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2138217033},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_syscfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2122731933},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1647780822},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1940537576},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pk_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:281676397},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_signature.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-500071007},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_chip.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-144584112},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha3.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1920173749},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_csr.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:401992527},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/mbedtls_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-941319006},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_prs.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1228427119},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/board/src/sl_board_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-104748454},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/sl_hal_sysrtc_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1639648435},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha256_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1840266278},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_se.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1295564343},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_entropy.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1770293398},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1438881046},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/base64_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1938385438},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecjpake.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1493975510},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/sl_hal_sysrtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-523224103},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1168040904},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sli_code_classification.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1618595656},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:300615726},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:89328765},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1552659152},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_values.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:613218289},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_hash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1274638196},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_hfxo_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1684643510},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_error.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2122050273},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1600536149},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/cmac_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:952832615},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream_eusart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-411246807},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha512_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2146416694},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1574787848},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/des.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1863183451},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1442960286},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_ldma.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1401247039},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_wdog.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:607897757},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/debug/src/sl_debug_swo.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1023895808},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/gcm_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-335711860},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-154473536},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_aes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:970359340},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-392223819},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_cache.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2137741083},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/src/sl_mbedtls.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-323344972},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1447200904},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals/dmadrv_signals.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1462779921},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_slist.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-953590213},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-204789911},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sl_hfxo_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1286369074},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;config/rail/profile_wmbus.restriction&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:879311661},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sli_clock_manager_init_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1156652257},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/rsa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2068227528},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x/rail_chip_specific.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1132495428},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm/cmsis_nvic_virtual.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1195757326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_msc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:665032437},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dma_descriptor.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:504730603},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_emu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2112828313},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1222713278},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/padlock.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1887725378},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_prs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-696650828},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/md5.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1053310714},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/camellia.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:576159557},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_devinfo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-400564063},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_attestation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1554850848},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1448261317},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/asn1write.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1626728908},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_ramfunc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-440195194},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_se.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1356715719},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_init_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-208533},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_gcc_preinclude.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1774014670},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Source/system_efr32fg23.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1896575954},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_reader.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:61771770},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_gcc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:965562395},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/sli_hfxo_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1634075703},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_psa_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:247451742},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_signature.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:170283401},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/src/sli_interrupt_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-209812558},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-431051984},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:149750972},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_vdac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1159086358},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-490405624},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/se_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:916346102},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_config_synonyms.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1010162114},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs5.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1865947758},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream_uart.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1624460598},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curve_types_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:126862545},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/md_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1458656376},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_burtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1082783693},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_composites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1810660689},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_signature.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-257511430},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_device_init_dcdc_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-942142679},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ctr_drbg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1261435000},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_util_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1153302833},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1235152493},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_14dbm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2147062319},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1916068175},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/mpu_armv8.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1593587326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_trace.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1706134553},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1494306959},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecp.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1524856915},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_ssl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1319523993},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1057123939},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_gpcrc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:703907103},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sl_hfxo_manager_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2051898812},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_smu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1014954948},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:387268379},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_core_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1510564519},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_usart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1495238711},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_slist.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2067642501},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/dmadrv_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1078500023},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_tls13_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1398163635},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1620019361},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-632084237},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcdrf.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-186166403},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_core.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1712987062},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/.crc_config.crc&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1928680529},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1699257200},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_atomic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1121716419},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/udelay/inc/sl_udelay.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-543325132},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_wmbus_support_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1238332046},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1281037243},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_enum.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:767800606},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_board_default_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1190240759},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1966218792},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_syscalls.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:260201778},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1961060320},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_misc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:16199566},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_msc_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-891071826},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_sysrtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:938507783},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/src/dmadrv.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-7321053},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_icache.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:61626087},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1381212837},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/system/src/sl_system_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1139771263},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_memory.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1336083326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_assert_error_codes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1762405158},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1290112594},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream_uart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-63165057},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/RTE_Components.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:794591649},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;config/rail/radio_settings.radioconf&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-990024099},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1458812370},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/chachapoly.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-288883639},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/memory_buffer_alloc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:260689301},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_x509.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:914424100},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_memory_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-115688910},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1564078607},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/build_info.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1206608469},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_functions.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1970326654},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:806677080},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_extra.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:15452979},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_values.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1614855224},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/src/sl_cos.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-103072488},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecdsa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1998450839},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/entropy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1120796653},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:200468512},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_mod_raw.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:98338191},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_scratchpad.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1931015410},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_aead.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1389358711},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1852634185},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:851670447},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-873321978},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sl_power_manager_debug.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1950774890},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_sleeptimer_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1439654632},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_ticket.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1388269939},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1424465558},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_callbacks_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-408222382},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/nist_kw.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-815635751},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/lmots.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-45330906},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_core_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1630536524},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_semailbox.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:717903982},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1066821294},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/debug.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-790134528},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dcdc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:387188575},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-930720039},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pem.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1284646079},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ecp_internal_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-483188695},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_key_handling.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-495765634},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sl_clock_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1559673446},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_platform.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1752441890},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:745723384},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_pti_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:777097589},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ecp_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-473604359},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_psa_acceleration.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:143907142},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/src/sl_led.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2064020521},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1466281258},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/oid.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:730266120},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/common/util/app_assert/app_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1177279121},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/check_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1441692535},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sli_sleeptimer_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:5545561},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2082865380},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_syscfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1187428177},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1147937499},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_string.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1433319706},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_adjust_auto_enabled.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-357561207},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:154024238},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/threading.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:358794250},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1930935454},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/src/sl_iostream_eusart.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-52895003},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2035428595},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/sha512.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:633335698},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/gcm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:992814453},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509_crl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-422647022},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/clocks/sl_device_clock_efr32xg23.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1591885863},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1023890460},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time_impl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1874694315},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1702069099},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1793066290},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/em_device.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-98245658},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_mbedtls_config_transform_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1492301550},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/cmac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-645565629},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_iadc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1794958899},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/lms.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1284831361},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/dmadrv.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2138123997},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sli_iostream_uart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-443677805},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ecdh.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1422911145},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/constant_time.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1272219106},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1304509351},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/src/se_aes.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1325615616},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-611299018},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1595050963},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/chacha20.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:90430987},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/dhm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1167584515},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_iostream_handles.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1149038433},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-768224831},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_contexts_primitives.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:199673500},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_driver_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:676538350},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Source/startup_efr32fg23.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1793247722},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_struct.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1473494301},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_mbedtls_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1330404375},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble/rail_ble.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1955543465},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_sysrtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1978477141},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_rail_util_callbacks.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1886858559},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_system.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-191182127},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_sizes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1386533414},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/toolchain/inc/sl_memory_region.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-41878248},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/cipher_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1378609183},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/poly1305.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1905127224},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pk_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1371657854},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:439749197},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:202006857},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_memory_manager_region_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1964077905},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bn_mul.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:892309499},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_cookie.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1383484083},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_20dbm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:227439383},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_timer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2070177492},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/mpu/src/sl_mpu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2022573313},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/asn1.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1267436546},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_osal_baremetal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:599826878},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sli_clock_manager_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-699056988},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_acmp.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-627636272},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sli_memory_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1598778044},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs_signals.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:413147011},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform_time.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:943723281},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ulfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:881188766},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1286089494},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/peripheral/inc/peripheral_sysrtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-618418128},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_omnipresent.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1124116814},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_cmu_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1608825958},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/board/inc/sl_board_control.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-844747529},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/inc/sl_memory_manager_region.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1567703618},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_pool.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1474384783},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/iostream/inc/sl_iostream.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:174152490},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-208826855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2069315458},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_rail_util_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1320033455},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/net_sockets.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1308256325},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_tls13_keys.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1527696937},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/x509.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1795536466},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1256806599},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_rail_util_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-738673425},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/nvm3_default_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1637484117},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/rsa_alt_helpers.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-228475054},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-116003679},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/mps_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1967305661},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/psa_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:721753331},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/src/sl_device_clock.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1718382020},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mailbox.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:331161287},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-48722375},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/src/sli_hfxo_manager_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:830472066},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/sl_rail_util_callbacks.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-678760573},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_defines.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2021516904},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;main.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:657608664},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:100548256},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sli_power_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2131986581},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_version_dependencies.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1562635797},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_functions.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-617547412},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/aes.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1719454461},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/system/src/sl_system_process_action.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1001663153},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_attestation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1754504484},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_core_cortexm.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-40574468},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-319395213},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-11363360},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lesense.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1457827890},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/src/sl_clock_manager_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1397938616},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/rail_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2120775187},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcd.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1404058854},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs7.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-395362872},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/src/sl_sleeptimer_hal_burtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1627734505},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/pkwrite.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-31036965},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_mbedtls_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-277379607},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus/rail_wmbus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-292831821},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-117500666},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/common/rail_mfm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-659205907},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/rail_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1996807673},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/tz_context.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1659747362},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/psa_crypto_client.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1346742557},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc/sl_hfxo_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1828452951},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-687976896},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_msc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-783053713},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_iostream_init_instances.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:692404961},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_10dbm_434M.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:844037243},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/psa_crypto_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1128401433},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1527196442},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:225711926},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_clock_manager_oscillator_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-241466492},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1603511496},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc/sli_sleeptimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:145180675},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/constant_time.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1166399062},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ecjpake_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:218536478},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/platform_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1497252592},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_board_control_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1581659387},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_keyscan.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:628405803},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/bignum_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-385596278},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_psa_builtin_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1328919745},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_builtin_composites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1893759768},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_flex_rail_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1152007068},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sl_mbedtls.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:307570691},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/compat-2.x.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-886224317},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-248130239},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1730920463},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/platform.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:510750835},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_init/inc/sl_device_init_dcdc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-317870393},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23b010f512im48.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1538534985},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1528133626},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/entropy_poll.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:4635592},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/inc/sl_power_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-932568054},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_timer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-513234271},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/src/sl_string.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1947422120},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/devices/sl_device_peripheral_hal_efr32xg23.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-414693197},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_protocol_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1540736376},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks/sli_rail_util_callbacks.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:403239966},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1587596792},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/aes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1041223671},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_emu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1134588491},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_i2c.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1715886909},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/src/em_msc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1217994009},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_pa_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:697245963},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_ldma.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:294637442},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_debug_swo_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:336662250},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager_dynamic_reservation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:479209915},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1899446502},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_emu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:119834836},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_burtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1249164574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_builtin_keys.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:991909460},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_eusart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1262872389},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/sha1_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:497985512},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sli_memory_manager_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1570829044},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/ssl_debug_helpers.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:899881076},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app_process.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:666221513},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_power_manager_handler.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1898860282},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app_process.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1053328523},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/platform.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2070856173},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pfmxpprf.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-435082262},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_event_handler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:588381536},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/alignment.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-112035855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/src/sl_simple_led.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:918526631},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar_defines.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-625120999},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_power_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1333247573},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_event_handler.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1987328998},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_internal_keys.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:711949574},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_iostream_eusart_vcom_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1138233491},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sli_se_manager_osal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2113520647},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:79781219},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-907311705},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_fsrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1226861810},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/sl_simple_led.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:419529284},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/core_cm33.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1576947287},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/radioconf_generation_log.json&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1275321501},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;readme.md&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:46459165},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_iostream_handles.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:267352300},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_gpcrc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:11503013},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldma.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-595404295},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ssl_ciphersuites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2021226836},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/pkcs12.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-911163279},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc/ccm_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1363022037},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sl_clock_manager_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1297545247},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_version_dependencies.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:194964729},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_simple_led_instances.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-54179911},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/mpu/inc/sl_mpu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1867587885},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_simple_led_instances.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:426801400},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/hkdf.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-267252086},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/ccm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1160274346},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_eusart_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1620568081},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/sl_interrupt_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1032531634},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_power_manager_init_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1634398877},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emdrv/common/inc/ecode.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1051405623},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/library/md_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-961503962},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_interrupt_manager_s2_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1179022542},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/clock_manager/inc/sli_clock_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1937731387},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/memory_manager/src/sl_memory_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1436602296},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/inc/sl_device_clock.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1913302493},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-355722532},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_mac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-323289492},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/emlib/inc/em_system.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-703227574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_status.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-185421498},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sl_se_manager_check_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1069133816},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/aria.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1263404770},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/common/inc/sl_bit.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1509628629},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src/sl_se_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2138740159},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/power_manager/src/sl_power_manager_debug.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:135845146},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/hardware/board/inc/sl_board_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:352788959},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_init_inst0_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1154762997},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/device_manager/src/sl_device_peripheral.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-796001333},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/system/inc/sl_system_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1481314655},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include/cmsis_compiler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1017116116},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_buram.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:419564313},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/driver/leddrv/inc/sl_led.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1218211254},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/service/udelay/src/sl_udelay_armv6m_gcc.S&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:295142276},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio_port.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:716399889},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/psa/crypto_se_driver.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-618155878},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_flex_rail_channel_selector_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1409863112},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc/sli_se_manager_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1305661877},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/util/third_party/mbedtls/include/mbedtls/timing.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1126843370},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1051970637},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1066014470},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mpahbram.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2120527228},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_management.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1039089946}]" projectCommon.ideId="simplicity-ide" projectCommon.importModeId="COPY" projectCommon.partId="mcu.arm.efr32.fg23.efr32fg23b010f512im48" projectCommon.savedStockVariables="{&quot;copiedSdkLocation&quot;:&quot;simplicity_sdk_2024.6.2&quot;,&quot;partOpn&quot;:&quot;efr32fg23b010f512im48&quot;}" projectCommon.sdkId="com.silabs.sdk.stack.sisdk:2024.6.2._901395569" projectCommon.toolchainId="com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="kcmca6s-application.com.silabs.ss.framework.ide.project.core.cdt.cdtMbsProjectType.**********" name="SLS CDT Project" projectType="com.silabs.ss.framework.ide.project.core.cdt.cdtMbsProjectType"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205;com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base.**********;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input.715928078">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="GNU ARM v12.2.1 - Default">
			<resource resourceType="PROJECT" workspacePath="/kcmca6s-application"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
</cproject>