/*
 * ql_wmbus_handle.h
 *
 *  Created on: 2024年10月28日
 *      Author: kahn.wei
 */

#ifndef __QL_WMBUS_HANDLE_H__
#define __QL_WMBUS_HANDLE_H__

#include "ql_include.h"
#include "rail_types.h"

typedef enum 
{
    S_PACKET_RECEIVED,
    S_RX_PACKET_ERROR,
    S_CALIBRATION_ERROR,
    S_IDLE,

    S_AUTO_RECV_TX,     //!< Sending auto received packets
    S_SCHEDULE_TX,      //!< Sending scheduled packets
    S_RESPONSE_DELAY,   //!< Waiting for to start response listening
    S_UNLIMITED_ACCESS, //!< Stay in RX until the next TX
    S_IDLE_OR_RX,       //!< Got to sleep or idle depending on the power manager or go back to RX if unlimited access is selected
    S_TX_DONE,          //!< Sending is successfully done
} state_t;

typedef struct 
{
    uint8_t wmbus_uart_recv_data[1024];
    uint16_t wmbus_uart_recv_len;
}ql_wmbus_uart_recv_data_info_t;

typedef struct 
{
    uint8_t cc;
    uint8_t acc;
}__attribute__((packed)) ci_8c_ell_frame_t;

typedef struct 
{
    uint8_t cc;
    uint8_t acc;
    uint32_t sn;
    uint16_t crc16;
}__attribute__((packed)) ci_8d_ell_frame_t;

typedef struct 
{
    uint8_t cc;
    uint8_t acc;
    uint16_t m2;
    struct
    {
        uint32_t dev_id;
        uint8_t ver;
        uint8_t dev;
    }a2;
}__attribute__((packed)) ci_8e_ell_frame_t;

typedef struct 
{
    uint8_t cc;
    uint8_t acc;
    uint16_t m2;
    struct 
    {
        uint32_t dev_id;
        uint8_t ver;
        uint8_t dev;
    }a2;
}__attribute__((packed)) ci_8f_ell_frame_t;

typedef struct 
{
    uint8_t ci_field;
    union
    {
        uint8_t raw_paylaod[16];
        ci_8c_ell_frame_t ci_8c;
        ci_8d_ell_frame_t ci_8d;
        ci_8e_ell_frame_t ci_8e;
        ci_8f_ell_frame_t ci_8f; 
    }ci_ell_type;
}ql_wmbus_ci_ell_data_t;

#define WMBUS_CI_EXTENDED_LINK_LAYER_CC_MAP(xx)         \
    xx(B, 0x80, "")                                     \
    xx(C, 0x40, "")                                     \
    xx(S, 0x20, "")                                     \
    xx(H, 0x10, "")                                     \
    xx(P, 0x08, "")                                     \
    xx(A, 0x04, "")                                     \
    xx(R, 0x02, "")                                     \
    xx(X, 0x01, "")                                     \

typedef enum
{
#define xx(code,idx,_) WMBUS_CI_EXTENDED_LINK_LAYER_CC_FIELD_##code=idx,
    WMBUS_CI_EXTENDED_LINK_LAYER_CC_MAP(xx)
#undef xx
}WMBUS_ELL_CC_FEILD;

typedef uint8_t wmbus_ci_field;
typedef void(*wmbus_app_func)(uint8_t *, uint32_t);

int16_t ql_wmbus_init(void);

void ql_wmbus_handle_loop(void);

extern ql_wmbus_uart_recv_data_info_t g_wmbus_uart_recv_data_info;

uint32_t ql_wmbus_get_tune_value(void);

uint8_t ql_wmbus_get_current_role(void);

RAIL_Handle_t ql_wmbus_get_rail_handle(void);

extern uint16_t g_wmbus_current_tx_channel;

uint8_t ql_wmbus_rf_channel_query_rssi_values(void);

#endif /* __QL_WMBUS_HANDLE_H__ */
