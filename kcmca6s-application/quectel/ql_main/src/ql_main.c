#include "ql_main.h"

enum { r0, r1, r2, r3, r12, lr, pc, psr};

void ql_application_query_build_time(ql_build_time_t *time)
{
   char mon_str[5] = { 0 };
   static char month_names[] = "JanFebMarAprMayJunJulAugSepOctNovDec";
   // hh:mm:ss
   sscanf(__TIME__, "%hd:%hd:%hd", &time->Hour, &time->Min, &time->Sec);

   // Mmm dd yyyy
   sscanf(__DATE__, "%s %hd %hd", mon_str, &time->Day, &time->Year);
   time->Mon = ((strstr(month_names, mon_str) - &month_names[0]) / 3 + 1);
}

void ql_main(void)
{
    ql_wdg_init();
    ql_uart_open(QL_UART_UART0_PORT);
    ql_uart_open(QL_UART_UART1_PORT);
    ql_printf("\r\n[APP]QUECTEL APPLICATION INIT\r\n");
    ql_build_time_t build_time;
    ql_application_query_build_time(&build_time);
    ql_printf("[APP]QUECTEL APPLICATION BUILD TIME: [%04d/%02d/%02d,%02d:%02d:%02d]\r\n",
            build_time.Year,
            build_time.Mon,
            build_time.Day,
            build_time.Hour,
            build_time.Min,
            build_time.Sec
    );

    ql_nvm_init();
    ql_param_cfg_init();
    ql_wmbus_init();
    ql_printf("[APP]QUECTEL APPLICATION INIT COMPLETE\r\n");

#ifndef __CUSTOMER_CLOU__
    ql_output_customer_detail_version_info();
#endif
}

void ql_handle_loop(void)
{
    ql_cmd_handle_loop();
    ql_wmbus_handle_loop();
    ql_wdg_feed();
}

static void stackDump(uint32_t stack[])
{
    // SCB寄存器信息
    uint32_t hfsr = SCB->HFSR;  // 硬故障状态寄存器
    uint32_t cfsr = SCB->CFSR;  // 配置和故障状态寄存器
    uint32_t mmfar = SCB->MMFAR; // 内存管理故障地址寄存器
    uint32_t bfar = SCB->BFAR;  // 总线故障地址寄存器
    uint32_t afsr = SCB->AFSR;  // 辅助故障状态寄存器
    static char msg[80];

    ql_printf("========== Hard Fault Detected ==========\r\n");
    ql_printf("Stacked Registers:\n");
    sprintf(msg, "[R0]  = 0x%08x\r\n", stack[r0]); ql_printf(msg);
    sprintf(msg, "[R1]  = 0x%08x\r\n", stack[r1]); ql_printf(msg);
    sprintf(msg, "[R2]  = 0x%08x\r\n", stack[r2]); ql_printf(msg);
    sprintf(msg, "[R3]  = 0x%08x\r\n", stack[r3]); ql_printf(msg);
    sprintf(msg, "[R12] = 0x%08x\r\n", stack[r12]); ql_printf(msg);
    sprintf(msg, "[LR]  = 0x%08x\r\n", stack[lr]); ql_printf(msg);
    sprintf(msg, "[PC]  = 0x%08x\r\n", stack[pc]); ql_printf(msg);
    sprintf(msg, "[PSR] = 0x%08x\r\n", stack[psr]); ql_printf(msg);

    ql_printf("\nSCB Registers:\n");
    ql_printf("HFSR : 0x%08X\n", hfsr);
    ql_printf("CFSR : 0x%08X\n", cfsr);
    ql_printf("MMFAR: 0x%08X\n", mmfar);
    ql_printf("BFAR : 0x%08X\n", bfar);
    ql_printf("AFSR : 0x%08X\n", afsr);
}

void ql_hardfault_handler_user_defined(uint32_t stack[])
{
    uint32_t cfsr = SCB->CFSR;
    uint32_t hfsr = SCB->HFSR;

    stackDump(stack);

    ql_printf("\nHard Fault Reason:\n");
    if (cfsr & 0x00000001) ql_printf("Memory Management Fault: Instruction Access Violation\n");
    if (cfsr & 0x00000002) ql_printf("Memory Management Fault: Data Access Violation\n");
    if (cfsr & 0x00000008) ql_printf("Memory Management Fault: Unstacking Error\n");
    if (cfsr & 0x00000010) ql_printf("Memory Management Fault: Stacking Error\n");
    if (cfsr & 0x00000080) ql_printf("Memory Management Fault: Address Register Valid\n");

    if (cfsr & 0x00000100) ql_printf("Bus Fault: Instruction Access Violation\n");      // SCB_CFSR_IBUSERR_Msk
    if (cfsr & 0x00000200) ql_printf("Bus Fault: Precise Data Access Violation\n");     // SCB_CFSR_PRECISERR_Msk
    if (cfsr & 0x00000400) ql_printf("Bus Fault: Imprecise Data Access Violation\n");   // SCB_CFSR_IMPRECISERR_Msk
    if (cfsr & 0x00000800) ql_printf("Bus Fault: Unstacking Error\n");                  // SCB_CFSR_UNSTKERR_Msk
    if (cfsr & 0x00001000) ql_printf("Bus Fault: Stacking Error\n");                    // SCB_CFSR_STKERR_Msk
    if (cfsr & 0x00008000) ql_printf("Bus Fault: Address Register Valid\n");            // SCB_CFSR_BFARVALID_Msk

    if (cfsr & 0x01000000) ql_printf("Usage Fault: Undefined Instruction\n");
    if (cfsr & 0x02000000) ql_printf("Usage Fault: Illegal State\n");
    if (cfsr & 0x04000000) ql_printf("Usage Fault: Invalid PC Load\n");
    if (cfsr & 0x08000000) ql_printf("Usage Fault: No Coprocessor\n");
    if (cfsr & 0x10000000) ql_printf("Usage Fault: Unaligned Access\n");
    if (cfsr & 0x20000000) ql_printf("Usage Fault: Divide by Zero\n");
    
    ql_printf("=========================================\r\n");
    __ASM volatile("BKPT #01");
    while (1);
}

void ql_hardfault_handler(void)
{
    __ASM("TST LR, #4");
    __ASM("ITE EQ");
    __ASM("MRSEQ R0, MSP");
    __ASM("MRSNE R0, PSP");
    __ASM("B ql_hardfault_handler_user_defined");
}

void HardFault_Handler(void)
{
    ql_hardfault_handler();
}

