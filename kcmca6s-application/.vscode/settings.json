{"files.associations": {"ql_main.h": "c", "ql_include.h": "c", "ql_nvm.h": "c", "nvm3_default.h": "c", "ql_param_cfg.h": "c", "ql_wmbus_handle.h": "c", "ql_cmd_handle.h": "c", "rail.h": "c", "ql_crc16.h": "c", "ql_uart.h": "c", "sl_event_handler.h": "c", "em_eusart.h": "c", "sl_iostream_uart.h": "c", "sl_iostream_eusart.h": "c", "sl_iostream.h": "c", "sl_iostream_eusart_vcom_config.h": "c", "sl_iostream_init_eusart_instances.h": "c", "stdint.h": "c", "sl_wmbus_support.h": "c", "em_usart.h": "c", "em_ldma.h": "c", "em_gpio.h": "c", "efr32fg23_eusart.h": "c", "efr32fg23b010f512im48.h": "c", "em_eusart_compat.h": "c", "ql_time.h": "c", "ql_version.h": "c", "ql_wmbus_rf_channel_cfg.h": "c"}}