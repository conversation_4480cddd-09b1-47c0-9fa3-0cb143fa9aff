<?xml version="1.0" encoding="UTF-8"?>
<project name="uc.module.setup.componentSetup.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc" propertyScope="project">
  <folder name="simplicity_sdk_2024.6.2">
    <folder name="platform">
      <folder name="Device">
        <folder name="SiliconLabs">
          <folder name="EFR32FG23">
            <folder name="Source">
              <file name="system_efr32fg23.c" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Source/system_efr32fg23.c"/>
              <file name="startup_efr32fg23.c" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Source/startup_efr32fg23.c"/>
            </folder>
            <folder name="Include">
              <file name="efr32fg23b010f512im48.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23b010f512im48.h"/>
              <file name="efr32fg23_acmp.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_acmp.h"/>
              <file name="efr32fg23_aes.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_aes.h"/>
              <file name="efr32fg23_buram.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_buram.h"/>
              <file name="efr32fg23_burtc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_burtc.h"/>
              <file name="efr32fg23_cmu.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_cmu.h"/>
              <file name="efr32fg23_dcdc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dcdc.h"/>
              <file name="efr32fg23_devinfo.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_devinfo.h"/>
              <file name="efr32fg23_dma_descriptor.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dma_descriptor.h"/>
              <file name="efr32fg23_dpll.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_dpll.h"/>
              <file name="efr32fg23_emu.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_emu.h"/>
              <file name="efr32fg23_eusart.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_eusart.h"/>
              <file name="efr32fg23_fsrco.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_fsrco.h"/>
              <file name="efr32fg23_gpcrc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpcrc.h"/>
              <file name="efr32fg23_gpio.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio.h"/>
              <file name="efr32fg23_gpio_port.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_gpio_port.h"/>
              <file name="efr32fg23_hfrco.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfrco.h"/>
              <file name="efr32fg23_hfxo.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_hfxo.h"/>
              <file name="efr32fg23_i2c.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_i2c.h"/>
              <file name="efr32fg23_iadc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_iadc.h"/>
              <file name="efr32fg23_icache.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_icache.h"/>
              <file name="efr32fg23_keyscan.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_keyscan.h"/>
              <file name="efr32fg23_lcd.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcd.h"/>
              <file name="efr32fg23_lcdrf.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lcdrf.h"/>
              <file name="efr32fg23_ldma.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldma.h"/>
              <file name="efr32fg23_ldmaxbar.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar.h"/>
              <file name="efr32fg23_ldmaxbar_defines.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ldmaxbar_defines.h"/>
              <file name="efr32fg23_lesense.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lesense.h"/>
              <file name="efr32fg23_letimer.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_letimer.h"/>
              <file name="efr32fg23_lfrco.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfrco.h"/>
              <file name="efr32fg23_lfxo.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_lfxo.h"/>
              <file name="efr32fg23_mailbox.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mailbox.h"/>
              <file name="efr32fg23_mpahbram.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_mpahbram.h"/>
              <file name="efr32fg23_msc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_msc.h"/>
              <file name="efr32fg23_pcnt.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pcnt.h"/>
              <file name="efr32fg23_pfmxpprf.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_pfmxpprf.h"/>
              <file name="efr32fg23_prs.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs.h"/>
              <file name="efr32fg23_prs_signals.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_prs_signals.h"/>
              <file name="efr32fg23_scratchpad.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_scratchpad.h"/>
              <file name="efr32fg23_semailbox.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_semailbox.h"/>
              <file name="efr32fg23_smu.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_smu.h"/>
              <file name="efr32fg23_syscfg.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_syscfg.h"/>
              <file name="efr32fg23_sysrtc.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_sysrtc.h"/>
              <file name="efr32fg23_timer.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_timer.h"/>
              <file name="efr32fg23_ulfrco.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_ulfrco.h"/>
              <file name="efr32fg23_usart.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_usart.h"/>
              <file name="efr32fg23_vdac.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_vdac.h"/>
              <file name="efr32fg23_wdog.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/efr32fg23_wdog.h"/>
              <file name="em_device.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/em_device.h"/>
              <file name="system_efr32fg23.h" linked="false" uri="studio:/sdk/platform/Device/SiliconLabs/EFR32FG23/Include/system_efr32fg23.h"/>
            </folder>
          </folder>
        </folder>
      </folder>
      <folder name="common">
        <folder name="inc">
          <file name="sl_atomic.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_atomic.h"/>
          <file name="sl_bit.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_bit.h"/>
          <file name="sl_code_classification.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_code_classification.h"/>
          <file name="sli_code_classification.h" linked="false" uri="studio:/sdk/platform/common/inc/sli_code_classification.h"/>
          <file name="sl_enum.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_enum.h"/>
          <file name="sl_assert.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_assert.h"/>
          <file name="sl_common.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_common.h"/>
          <file name="sl_core.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_core.h"/>
          <file name="sl_string.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_string.h"/>
          <file name="sl_slist.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_slist.h"/>
          <file name="sl_status.h" linked="false" uri="studio:/sdk/platform/common/inc/sl_status.h"/>
        </folder>
        <folder name="src">
          <file name="sl_assert.c" linked="false" uri="studio:/sdk/platform/common/src/sl_assert.c"/>
          <file name="sl_core_cortexm.c" linked="false" uri="studio:/sdk/platform/common/src/sl_core_cortexm.c"/>
          <file name="sl_string.c" linked="false" uri="studio:/sdk/platform/common/src/sl_string.c"/>
          <file name="sl_syscalls.c" linked="false" uri="studio:/sdk/platform/common/src/sl_syscalls.c"/>
          <file name="sl_slist.c" linked="false" uri="studio:/sdk/platform/common/src/sl_slist.c"/>
        </folder>
        <folder name="toolchain">
          <folder name="inc">
            <file name="sl_memory_region.h" linked="false" uri="studio:/sdk/platform/common/toolchain/inc/sl_memory_region.h"/>
            <file name="sl_memory.h" linked="false" uri="studio:/sdk/platform/common/toolchain/inc/sl_memory.h"/>
            <file name="sl_gcc_preinclude.h" linked="false" uri="studio:/sdk/platform/common/toolchain/inc/sl_gcc_preinclude.h"/>
          </folder>
        </folder>
      </folder>
      <folder name="service">
        <folder name="clock_manager">
          <folder name="src">
            <file name="sl_clock_manager_init.c" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sl_clock_manager_init.c"/>
            <file name="sli_clock_manager_init_hal.h" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sli_clock_manager_init_hal.h"/>
            <file name="sl_clock_manager_init_hal_s2.c" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sl_clock_manager_init_hal_s2.c"/>
            <file name="sl_clock_manager.c" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sl_clock_manager.c"/>
            <file name="sli_clock_manager_hal.h" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sli_clock_manager_hal.h"/>
            <file name="sl_clock_manager_hal_s2.c" linked="false" uri="studio:/sdk/platform/service/clock_manager/src/sl_clock_manager_hal_s2.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_clock_manager_init.h" linked="false" uri="studio:/sdk/platform/service/clock_manager/inc/sl_clock_manager_init.h"/>
            <file name="sl_clock_manager.h" linked="false" uri="studio:/sdk/platform/service/clock_manager/inc/sl_clock_manager.h"/>
            <file name="sli_clock_manager.h" linked="false" uri="studio:/sdk/platform/service/clock_manager/inc/sli_clock_manager.h"/>
          </folder>
        </folder>
        <folder name="device_manager">
          <folder name="src">
            <file name="sl_device_clock.c" linked="false" uri="studio:/sdk/platform/service/device_manager/src/sl_device_clock.c"/>
            <file name="sl_device_peripheral.c" linked="false" uri="studio:/sdk/platform/service/device_manager/src/sl_device_peripheral.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_device_clock.h" linked="false" uri="studio:/sdk/platform/service/device_manager/inc/sl_device_clock.h"/>
            <file name="sl_device_peripheral.h" linked="false" uri="studio:/sdk/platform/service/device_manager/inc/sl_device_peripheral.h"/>
          </folder>
          <folder name="clocks">
            <file name="sl_device_clock_efr32xg23.c" linked="false" uri="studio:/sdk/platform/service/device_manager/clocks/sl_device_clock_efr32xg23.c"/>
          </folder>
          <folder name="devices">
            <file name="sl_device_peripheral_hal_efr32xg23.c" linked="false" uri="studio:/sdk/platform/service/device_manager/devices/sl_device_peripheral_hal_efr32xg23.c"/>
          </folder>
        </folder>
        <folder name="device_init">
          <folder name="src">
            <file name="sl_device_init_dcdc_s2.c" linked="false" uri="studio:/sdk/platform/service/device_init/src/sl_device_init_dcdc_s2.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_device_init_dcdc.h" linked="false" uri="studio:/sdk/platform/service/device_init/inc/sl_device_init_dcdc.h"/>
          </folder>
        </folder>
        <folder name="hfxo_manager">
          <folder name="src">
            <file name="sl_hfxo_manager.c" linked="false" uri="studio:/sdk/platform/service/hfxo_manager/src/sl_hfxo_manager.c"/>
            <file name="sl_hfxo_manager_hal_s2.c" linked="false" uri="studio:/sdk/platform/service/hfxo_manager/src/sl_hfxo_manager_hal_s2.c"/>
            <file name="sli_hfxo_manager_internal.h" linked="false" uri="studio:/sdk/platform/service/hfxo_manager/src/sli_hfxo_manager_internal.h"/>
          </folder>
          <folder name="inc">
            <file name="sl_hfxo_manager.h" linked="false" uri="studio:/sdk/platform/service/hfxo_manager/inc/sl_hfxo_manager.h"/>
            <file name="sli_hfxo_manager.h" linked="false" uri="studio:/sdk/platform/service/hfxo_manager/inc/sli_hfxo_manager.h"/>
          </folder>
        </folder>
        <folder name="interrupt_manager">
          <folder name="src">
            <file name="sli_interrupt_manager.h" linked="false" uri="studio:/sdk/platform/service/interrupt_manager/src/sli_interrupt_manager.h"/>
            <file name="sl_interrupt_manager_cortexm.c" linked="false" uri="studio:/sdk/platform/service/interrupt_manager/src/sl_interrupt_manager_cortexm.c"/>
          </folder>
          <folder name="inc">
            <folder name="arm">
              <file name="cmsis_nvic_virtual.h" linked="false" uri="studio:/sdk/platform/service/interrupt_manager/inc/arm/cmsis_nvic_virtual.h"/>
            </folder>
            <file name="sl_interrupt_manager.h" linked="false" uri="studio:/sdk/platform/service/interrupt_manager/inc/sl_interrupt_manager.h"/>
          </folder>
        </folder>
        <folder name="iostream">
          <folder name="src">
            <file name="sl_iostream.c" linked="false" uri="studio:/sdk/platform/service/iostream/src/sl_iostream.c"/>
            <file name="sl_iostream_eusart.c" linked="false" uri="studio:/sdk/platform/service/iostream/src/sl_iostream_eusart.c"/>
            <file name="sl_iostream_uart.c" linked="false" uri="studio:/sdk/platform/service/iostream/src/sl_iostream_uart.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_iostream.h" linked="false" uri="studio:/sdk/platform/service/iostream/inc/sl_iostream.h"/>
            <file name="sl_iostream_eusart.h" linked="false" uri="studio:/sdk/platform/service/iostream/inc/sl_iostream_eusart.h"/>
            <file name="sl_iostream_uart.h" linked="false" uri="studio:/sdk/platform/service/iostream/inc/sl_iostream_uart.h"/>
            <file name="sli_iostream_uart.h" linked="false" uri="studio:/sdk/platform/service/iostream/inc/sli_iostream_uart.h"/>
          </folder>
        </folder>
        <folder name="memory_manager">
          <folder name="src">
            <file name="sl_memory_manager.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sl_memory_manager.c"/>
            <file name="sl_memory_manager_dynamic_reservation.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sl_memory_manager_dynamic_reservation.c"/>
            <file name="sli_memory_manager_common.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sli_memory_manager_common.c"/>
            <file name="sl_memory_manager_retarget.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sl_memory_manager_retarget.c"/>
            <file name="sli_memory_manager.h" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sli_memory_manager.h"/>
            <file name="sl_memory_manager_pool.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sl_memory_manager_pool.c"/>
            <file name="sl_memory_manager_region.c" linked="false" uri="studio:/sdk/platform/service/memory_manager/src/sl_memory_manager_region.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_memory_manager.h" linked="false" uri="studio:/sdk/platform/service/memory_manager/inc/sl_memory_manager.h"/>
            <file name="sl_memory_manager_region.h" linked="false" uri="studio:/sdk/platform/service/memory_manager/inc/sl_memory_manager_region.h"/>
          </folder>
        </folder>
        <folder name="mpu">
          <folder name="src">
            <file name="sl_mpu.c" linked="false" uri="studio:/sdk/platform/service/mpu/src/sl_mpu.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_mpu.h" linked="false" uri="studio:/sdk/platform/service/mpu/inc/sl_mpu.h"/>
          </folder>
        </folder>
        <folder name="power_manager">
          <folder name="src">
            <file name="sl_power_manager.c" linked="false" uri="studio:/sdk/platform/service/power_manager/src/sl_power_manager.c"/>
            <file name="sl_power_manager_debug.c" linked="false" uri="studio:/sdk/platform/service/power_manager/src/sl_power_manager_debug.c"/>
            <file name="sl_power_manager_hal_s2.c" linked="false" uri="studio:/sdk/platform/service/power_manager/src/sl_power_manager_hal_s2.c"/>
            <file name="sli_power_manager_private.h" linked="false" uri="studio:/sdk/platform/service/power_manager/src/sli_power_manager_private.h"/>
          </folder>
          <folder name="inc">
            <file name="sl_power_manager.h" linked="false" uri="studio:/sdk/platform/service/power_manager/inc/sl_power_manager.h"/>
            <file name="sl_power_manager_debug.h" linked="false" uri="studio:/sdk/platform/service/power_manager/inc/sl_power_manager_debug.h"/>
            <file name="sli_power_manager.h" linked="false" uri="studio:/sdk/platform/service/power_manager/inc/sli_power_manager.h"/>
          </folder>
        </folder>
        <folder name="system">
          <folder name="src">
            <file name="sl_system_init.c" linked="false" uri="studio:/sdk/platform/service/system/src/sl_system_init.c"/>
            <file name="sl_system_process_action.c" linked="false" uri="studio:/sdk/platform/service/system/src/sl_system_process_action.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_system_init.h" linked="false" uri="studio:/sdk/platform/service/system/inc/sl_system_init.h"/>
            <file name="sl_system_process_action.h" linked="false" uri="studio:/sdk/platform/service/system/inc/sl_system_process_action.h"/>
          </folder>
        </folder>
        <folder name="sleeptimer">
          <folder name="src">
            <file name="sl_sleeptimer.c" linked="false" uri="studio:/sdk/platform/service/sleeptimer/src/sl_sleeptimer.c"/>
            <file name="sl_sleeptimer_hal_sysrtc.c" linked="false" uri="studio:/sdk/platform/service/sleeptimer/src/sl_sleeptimer_hal_sysrtc.c"/>
            <file name="sl_sleeptimer_hal_burtc.c" linked="false" uri="studio:/sdk/platform/service/sleeptimer/src/sl_sleeptimer_hal_burtc.c"/>
            <file name="sl_sleeptimer_hal_timer.c" linked="false" uri="studio:/sdk/platform/service/sleeptimer/src/sl_sleeptimer_hal_timer.c"/>
            <file name="sli_sleeptimer_hal.h" linked="false" uri="studio:/sdk/platform/service/sleeptimer/src/sli_sleeptimer_hal.h"/>
          </folder>
          <folder name="inc">
            <file name="sl_sleeptimer.h" linked="false" uri="studio:/sdk/platform/service/sleeptimer/inc/sl_sleeptimer.h"/>
            <file name="sli_sleeptimer.h" linked="false" uri="studio:/sdk/platform/service/sleeptimer/inc/sli_sleeptimer.h"/>
          </folder>
        </folder>
        <folder name="udelay">
          <folder name="src">
            <file name="sl_udelay.c" linked="false" uri="studio:/sdk/platform/service/udelay/src/sl_udelay.c"/>
            <file name="sl_udelay_armv6m_gcc.S" linked="false" uri="studio:/sdk/platform/service/udelay/src/sl_udelay_armv6m_gcc.S"/>
          </folder>
          <folder name="inc">
            <file name="sl_udelay.h" linked="false" uri="studio:/sdk/platform/service/udelay/inc/sl_udelay.h"/>
          </folder>
        </folder>
      </folder>
      <folder name="CMSIS">
        <folder name="Core">
          <folder name="Include">
            <file name="cmsis_compiler.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/cmsis_compiler.h"/>
            <file name="cmsis_version.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/cmsis_version.h"/>
            <file name="tz_context.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/tz_context.h"/>
            <file name="cmsis_gcc.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/cmsis_gcc.h"/>
            <file name="core_cm33.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/core_cm33.h"/>
            <file name="mpu_armv8.h" linked="false" uri="studio:/sdk/platform/CMSIS/Core/Include/mpu_armv8.h"/>
          </folder>
        </folder>
      </folder>
      <folder name="driver">
        <folder name="debug">
          <folder name="src">
            <file name="sl_debug_swo.c" linked="false" uri="studio:/sdk/platform/driver/debug/src/sl_debug_swo.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_debug_swo.h" linked="false" uri="studio:/sdk/platform/driver/debug/inc/sl_debug_swo.h"/>
          </folder>
        </folder>
        <folder name="leddrv">
          <folder name="src">
            <file name="sl_led.c" linked="false" uri="studio:/sdk/platform/driver/leddrv/src/sl_led.c"/>
            <file name="sl_simple_led.c" linked="false" uri="studio:/sdk/platform/driver/leddrv/src/sl_simple_led.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_led.h" linked="false" uri="studio:/sdk/platform/driver/leddrv/inc/sl_led.h"/>
            <file name="sl_simple_led.h" linked="false" uri="studio:/sdk/platform/driver/leddrv/inc/sl_simple_led.h"/>
          </folder>
        </folder>
      </folder>
      <folder name="emdrv">
        <folder name="dmadrv">
          <folder name="src">
            <file name="dmadrv.c" linked="false" uri="studio:/sdk/platform/emdrv/dmadrv/src/dmadrv.c"/>
          </folder>
          <folder name="inc">
            <folder name="s2_signals">
              <file name="dmadrv_signals.h" linked="false" uri="studio:/sdk/platform/emdrv/dmadrv/inc/s2_signals/dmadrv_signals.h"/>
            </folder>
            <file name="dmadrv.h" linked="false" uri="studio:/sdk/platform/emdrv/dmadrv/inc/dmadrv.h"/>
          </folder>
        </folder>
        <folder name="common">
          <folder name="inc">
            <file name="ecode.h" linked="false" uri="studio:/sdk/platform/emdrv/common/inc/ecode.h"/>
          </folder>
        </folder>
        <folder name="nvm3">
          <folder name="inc">
            <file name="nvm3_default.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3_default.h"/>
            <file name="nvm3.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3.h"/>
            <file name="nvm3_generic.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3_generic.h"/>
            <file name="nvm3_hal.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3_hal.h"/>
            <file name="nvm3_hal_flash.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3_hal_flash.h"/>
            <file name="nvm3_lock.h" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/inc/nvm3_lock.h"/>
          </folder>
          <folder name="src">
            <file name="nvm3_default_common_linker.c" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/src/nvm3_default_common_linker.c"/>
            <file name="nvm3_hal_flash.c" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/src/nvm3_hal_flash.c"/>
            <file name="nvm3_lock.c" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/src/nvm3_lock.c"/>
          </folder>
          <folder name="lib">
            <file name="libnvm3_CM33_gcc.a" linked="false" uri="studio:/sdk/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a"/>
          </folder>
        </folder>
      </folder>
      <folder name="emlib">
        <folder name="src">
          <file name="em_burtc.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_burtc.c"/>
          <file name="em_cmu.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_cmu.c"/>
          <file name="em_core.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_core.c"/>
          <file name="em_emu.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_emu.c"/>
          <file name="em_eusart.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_eusart.c"/>
          <file name="em_gpcrc.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_gpcrc.c"/>
          <file name="em_gpio.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_gpio.c"/>
          <file name="em_ldma.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_ldma.c"/>
          <file name="em_msc.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_msc.c"/>
          <file name="em_prs.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_prs.c"/>
          <file name="em_se.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_se.c"/>
          <file name="em_system.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_system.c"/>
          <file name="em_timer.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_timer.c"/>
          <file name="em_usart.c" linked="false" uri="studio:/sdk/platform/emlib/src/em_usart.c"/>
        </folder>
        <folder name="inc">
          <file name="em_burtc.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_burtc.h"/>
          <file name="em_chip.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_chip.h"/>
          <file name="em_cmu.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_cmu.h"/>
          <file name="em_cmu_compat.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_cmu_compat.h"/>
          <file name="sli_em_cmu.h" linked="false" uri="studio:/sdk/platform/emlib/inc/sli_em_cmu.h"/>
          <file name="em_assert.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_assert.h"/>
          <file name="em_bus.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_bus.h"/>
          <file name="em_common.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_common.h"/>
          <file name="em_ramfunc.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_ramfunc.h"/>
          <file name="em_version.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_version.h"/>
          <file name="em_core.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_core.h"/>
          <file name="em_core_generic.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_core_generic.h"/>
          <file name="em_emu.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_emu.h"/>
          <file name="em_eusart.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_eusart.h"/>
          <file name="em_eusart_compat.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_eusart_compat.h"/>
          <file name="em_gpcrc.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_gpcrc.h"/>
          <file name="em_gpio.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_gpio.h"/>
          <file name="em_ldma.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_ldma.h"/>
          <file name="em_msc.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_msc.h"/>
          <file name="em_msc_compat.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_msc_compat.h"/>
          <file name="em_prs.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_prs.h"/>
          <file name="em_se.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_se.h"/>
          <file name="em_syscfg.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_syscfg.h"/>
          <file name="em_system.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_system.h"/>
          <file name="em_system_generic.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_system_generic.h"/>
          <file name="em_timer.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_timer.h"/>
          <file name="em_usart.h" linked="false" uri="studio:/sdk/platform/emlib/inc/em_usart.h"/>
        </folder>
      </folder>
      <folder name="peripheral">
        <folder name="src">
          <file name="sl_hal_sysrtc.c" linked="false" uri="studio:/sdk/platform/peripheral/src/sl_hal_sysrtc.c"/>
        </folder>
        <folder name="inc">
          <file name="sl_hal_sysrtc.h" linked="false" uri="studio:/sdk/platform/peripheral/inc/sl_hal_sysrtc.h"/>
          <file name="sl_hal_sysrtc_compat.h" linked="false" uri="studio:/sdk/platform/peripheral/inc/sl_hal_sysrtc_compat.h"/>
          <file name="peripheral_sysrtc.h" linked="false" uri="studio:/sdk/platform/peripheral/inc/peripheral_sysrtc.h"/>
          <file name="peripheral_sysrtc_compat.h" linked="false" uri="studio:/sdk/platform/peripheral/inc/peripheral_sysrtc_compat.h"/>
        </folder>
      </folder>
      <folder name="security">
        <folder name="sl_component">
          <folder name="sl_mbedtls_support">
            <folder name="config">
              <file name="sli_mbedtls_acceleration.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_acceleration.h"/>
              <file name="sli_mbedtls_omnipresent.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_omnipresent.h"/>
              <file name="sli_psa_acceleration.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/config/sli_psa_acceleration.h"/>
            </folder>
            <folder name="src">
              <file name="sl_mbedtls.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/src/sl_mbedtls.c"/>
              <file name="se_aes.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/src/se_aes.c"/>
            </folder>
            <folder name="inc">
              <file name="sl_mbedtls.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/sl_mbedtls.h"/>
              <file name="aes_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/aes_alt.h"/>
              <file name="ccm_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/ccm_alt.h"/>
              <file name="cmac_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/cmac_alt.h"/>
              <file name="se_management.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/se_management.h"/>
              <file name="sha1_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/sha1_alt.h"/>
              <file name="sha256_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/sha256_alt.h"/>
              <file name="sha512_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/sha512_alt.h"/>
              <file name="gcm_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/gcm_alt.h"/>
              <file name="ecjpake_alt.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/ecjpake_alt.h"/>
              <file name="sl_psa_values.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_values.h"/>
            </folder>
          </folder>
          <folder name="sl_psa_driver">
            <folder name="inc">
              <file name="sli_psa_driver_features.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_features.h"/>
              <file name="sli_psa_driver_common.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_common.h"/>
              <file name="sli_se_version_dependencies.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_version_dependencies.h"/>
              <file name="sli_se_driver_aead.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_aead.h"/>
              <file name="sli_se_driver_cipher.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_cipher.h"/>
              <file name="sli_se_driver_key_derivation.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_derivation.h"/>
              <file name="sli_se_driver_key_management.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_management.h"/>
              <file name="sli_se_driver_mac.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_mac.h"/>
              <file name="sli_se_opaque_functions.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_functions.h"/>
              <file name="sli_se_opaque_types.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_types.h"/>
              <file name="sli_se_transparent_functions.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_functions.h"/>
              <file name="sli_se_transparent_types.h" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_types.h"/>
            </folder>
            <folder name="src">
              <file name="sli_psa_driver_init.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_init.c"/>
              <file name="sli_psa_driver_common.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_common.c"/>
              <file name="sli_se_version_dependencies.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_version_dependencies.c"/>
              <file name="sli_se_driver_builtin_keys.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_builtin_keys.c"/>
              <file name="sli_se_driver_key_management.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_management.c"/>
              <file name="sli_se_driver_key_derivation.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_derivation.c"/>
              <file name="sli_se_driver_aead.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_aead.c"/>
              <file name="sli_se_driver_mac.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_mac.c"/>
              <file name="sli_se_driver_signature.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_signature.c"/>
              <file name="sli_se_driver_cipher.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_cipher.c"/>
              <file name="sli_se_transparent_driver_cipher.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_cipher.c"/>
              <file name="sli_se_transparent_driver_hash.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_hash.c"/>
              <file name="sli_se_transparent_driver_aead.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_aead.c"/>
              <file name="sli_se_transparent_driver_mac.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_mac.c"/>
              <file name="sli_se_transparent_key_derivation.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_key_derivation.c"/>
              <file name="sli_se_opaque_driver_mac.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_mac.c"/>
              <file name="sli_se_opaque_key_derivation.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_key_derivation.c"/>
              <file name="sli_se_opaque_driver_cipher.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_cipher.c"/>
              <file name="sli_se_opaque_driver_aead.c" linked="false" uri="studio:/sdk/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_aead.c"/>
            </folder>
          </folder>
          <folder name="se_manager">
            <folder name="src">
              <file name="sl_se_manager.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager.c"/>
              <file name="sl_se_manager_util.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_util.c"/>
              <file name="sl_se_manager_cipher.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_cipher.c"/>
              <file name="sl_se_manager_entropy.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_entropy.c"/>
              <file name="sl_se_manager_hash.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_hash.c"/>
              <file name="sl_se_manager_key_derivation.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_key_derivation.c"/>
              <file name="sl_se_manager_key_handling.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_key_handling.c"/>
              <file name="sl_se_manager_signature.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_signature.c"/>
              <file name="sl_se_manager_attestation.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sl_se_manager_attestation.c"/>
              <file name="sli_se_manager_mailbox.c" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sli_se_manager_mailbox.c"/>
              <file name="sli_se_manager_osal.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sli_se_manager_osal.h"/>
              <file name="sli_se_manager_osal_baremetal.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/src/sli_se_manager_osal_baremetal.h"/>
            </folder>
            <folder name="inc">
              <file name="sl_se_manager.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager.h"/>
              <file name="sl_se_manager_defines.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_defines.h"/>
              <file name="sl_se_manager_types.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_types.h"/>
              <file name="sl_se_manager_util.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_util.h"/>
              <file name="sli_se_manager_features.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sli_se_manager_features.h"/>
              <file name="sl_se_manager_internal_keys.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_internal_keys.h"/>
              <file name="sli_se_manager_internal.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sli_se_manager_internal.h"/>
              <file name="sl_se_manager_check_config.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_check_config.h"/>
              <file name="sl_se_manager_config.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_config.h"/>
              <file name="sl_se_manager_cipher.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_cipher.h"/>
              <file name="sl_se_manager_entropy.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_entropy.h"/>
              <file name="sl_se_manager_hash.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_hash.h"/>
              <file name="sl_se_manager_key_derivation.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_key_derivation.h"/>
              <file name="sl_se_manager_key_handling.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_key_handling.h"/>
              <file name="sl_se_manager_signature.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_signature.h"/>
              <file name="sl_se_manager_attestation.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sl_se_manager_attestation.h"/>
              <file name="sli_se_manager_mailbox.h" linked="false" uri="studio:/sdk/platform/security/sl_component/se_manager/inc/sli_se_manager_mailbox.h"/>
            </folder>
          </folder>
        </folder>
      </folder>
      <folder name="radio">
        <folder name="rail_lib">
          <folder name="common">
            <file name="rail.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/common/rail.h"/>
            <file name="rail_mfm.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/common/rail_mfm.h"/>
            <file name="rail_assert_error_codes.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/common/rail_assert_error_codes.h"/>
            <file name="rail_types.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/common/rail_types.h"/>
            <file name="rail_features.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/common/rail_features.h"/>
          </folder>
          <folder name="protocol">
            <folder name="ble">
              <file name="rail_ble.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/protocol/ble/rail_ble.h"/>
            </folder>
            <folder name="ieee802154">
              <file name="rail_ieee802154.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/protocol/ieee802154/rail_ieee802154.h"/>
            </folder>
            <folder name="wmbus">
              <file name="rail_wmbus.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/protocol/wmbus/rail_wmbus.h"/>
            </folder>
            <folder name="zwave">
              <file name="rail_zwave.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/protocol/zwave/rail_zwave.h"/>
            </folder>
            <folder name="sidewalk">
              <file name="rail_sidewalk.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/protocol/sidewalk/rail_sidewalk.h"/>
            </folder>
          </folder>
          <folder name="chip">
            <folder name="efr32">
              <folder name="efr32xg2x">
                <file name="rail_chip_specific.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/chip/efr32/efr32xg2x/rail_chip_specific.h"/>
              </folder>
            </folder>
          </folder>
          <folder name="autogen">
            <folder name="librail_release">
              <file name="librail_efr32xg23_gcc_release.a" linked="false" uri="studio:/sdk/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a"/>
            </folder>
          </folder>
          <folder name="plugin">
            <folder name="rail_util_callbacks">
              <file name="sl_rail_util_callbacks.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_callbacks/sl_rail_util_callbacks.h"/>
              <file name="sli_rail_util_callbacks.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_callbacks/sli_rail_util_callbacks.h"/>
            </folder>
            <folder name="pa-conversions">
              <folder name="efr32xg23">
                <file name="sl_rail_util_pa_curves_14dbm.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_14dbm.h"/>
                <file name="sl_rail_util_pa_curves_20dbm.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_20dbm.h"/>
                <file name="sl_rail_util_pa_curves_10dbm_434M.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23/sl_rail_util_pa_curves_10dbm_434M.h"/>
              </folder>
              <file name="pa_conversions_efr32.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.c"/>
              <file name="pa_curves_efr32.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.c"/>
              <file name="pa_conversions_efr32.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.h"/>
              <file name="pa_curve_types_efr32.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/pa_curve_types_efr32.h"/>
              <file name="pa_curves_efr32.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.h"/>
            </folder>
            <folder name="rail_util_power_manager_init">
              <file name="sl_rail_util_power_manager_init.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.c"/>
              <file name="sl_rail_util_power_manager_init.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.h"/>
            </folder>
            <folder name="rail_util_protocol">
              <file name="sl_rail_util_protocol.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.c"/>
              <file name="sl_rail_util_protocol.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol.h"/>
              <file name="sl_rail_util_protocol_types.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_protocol/sl_rail_util_protocol_types.h"/>
            </folder>
            <folder name="rail_util_pti">
              <file name="sl_rail_util_pti.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.c"/>
              <file name="sl_rail_util_pti.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.h"/>
            </folder>
            <folder name="rail_util_rssi">
              <file name="sl_rail_util_rssi.c" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.c"/>
              <file name="sl_rail_util_rssi.h" linked="false" uri="studio:/sdk/platform/radio/rail_lib/plugin/rail_util_rssi/sl_rail_util_rssi.h"/>
            </folder>
          </folder>
        </folder>
      </folder>
    </folder>
    <folder name="app">
      <folder name="common">
        <folder name="util">
          <folder name="app_assert">
            <file name="app_assert.h" linked="false" uri="studio:/sdk/app/common/util/app_assert/app_assert.h"/>
            <file name="sl_app_assert.h" linked="false" uri="studio:/sdk/app/common/util/app_assert/sl_app_assert.h"/>
          </folder>
        </folder>
      </folder>
      <folder name="flex">
        <folder name="component">
          <folder name="rail">
            <folder name="simple_rail_assistance">
              <file name="simple_rail_assistance.c" linked="false" uri="studio:/sdk/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.c"/>
              <file name="simple_rail_assistance.h" linked="false" uri="studio:/sdk/app/flex/component/rail/simple_rail_assistance/simple_rail_assistance.h"/>
            </folder>
            <folder name="sl_flex_rail_channel_selector">
              <file name="sl_flex_rail_channel_selector.c" linked="false" uri="studio:/sdk/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.c"/>
              <file name="sl_flex_rail_channel_selector.h" linked="false" uri="studio:/sdk/app/flex/component/rail/sl_flex_rail_channel_selector/sl_flex_rail_channel_selector.h"/>
            </folder>
            <folder name="sl_wmbus_support">
              <file name="sl_wmbus_support.c" linked="false" uri="studio:/sdk/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.c"/>
              <file name="sl_wmbus_support.h" linked="false" uri="studio:/sdk/app/flex/component/rail/sl_wmbus_support/sl_wmbus_support.h"/>
            </folder>
          </folder>
        </folder>
      </folder>
    </folder>
    <folder name="hardware">
      <folder name="board">
        <folder name="src">
          <file name="sl_board_init.c" linked="false" uri="studio:/sdk/hardware/board/src/sl_board_init.c"/>
          <file name="sl_board_control_gpio.c" linked="false" uri="studio:/sdk/hardware/board/src/sl_board_control_gpio.c"/>
        </folder>
        <folder name="inc">
          <file name="sl_board_control.h" linked="false" uri="studio:/sdk/hardware/board/inc/sl_board_control.h"/>
          <file name="sl_board_init.h" linked="false" uri="studio:/sdk/hardware/board/inc/sl_board_init.h"/>
        </folder>
      </folder>
      <folder name="driver">
        <folder name="configuration_over_swo">
          <folder name="src">
            <file name="sl_cos.c" linked="false" uri="studio:/sdk/hardware/driver/configuration_over_swo/src/sl_cos.c"/>
          </folder>
          <folder name="inc">
            <file name="sl_cos.h" linked="false" uri="studio:/sdk/hardware/driver/configuration_over_swo/inc/sl_cos.h"/>
          </folder>
        </folder>
      </folder>
    </folder>
    <folder name="util">
      <folder name="third_party">
        <folder name="mbedtls">
          <folder name="library">
            <file name="aes.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/aes.c"/>
            <file name="constant_time.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/constant_time.c"/>
            <file name="threading.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/threading.c"/>
            <file name="platform.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/platform.c"/>
            <file name="platform_util.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/platform_util.c"/>
            <file name="psa_util.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/psa_util.c"/>
            <file name="alignment.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/alignment.h"/>
            <file name="bn_mul.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/bn_mul.h"/>
            <file name="base64_internal.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/base64_internal.h"/>
            <file name="bignum_core.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/bignum_core.h"/>
            <file name="bignum_mod.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/bignum_mod.h"/>
            <file name="bignum_mod_raw.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/bignum_mod_raw.h"/>
            <file name="bignum_mod_raw_invasive.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/bignum_mod_raw_invasive.h"/>
            <file name="cipher_wrap.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/cipher_wrap.h"/>
            <file name="common.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/common.h"/>
            <file name="constant_time_impl.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/constant_time_impl.h"/>
            <file name="constant_time_internal.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/constant_time_internal.h"/>
            <file name="ecp_internal_alt.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ecp_internal_alt.h"/>
            <file name="ecp_invasive.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ecp_invasive.h"/>
            <file name="entropy_poll.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/entropy_poll.h"/>
            <file name="lmots.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/lmots.h"/>
            <file name="md_psa.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/md_psa.h"/>
            <file name="md_wrap.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/md_wrap.h"/>
            <file name="mps_common.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/mps_common.h"/>
            <file name="mps_error.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/mps_error.h"/>
            <file name="mps_reader.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/mps_reader.h"/>
            <file name="mps_trace.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/mps_trace.h"/>
            <file name="padlock.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/padlock.h"/>
            <file name="pk_internal.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/pk_internal.h"/>
            <file name="pk_wrap.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/pk_wrap.h"/>
            <file name="pkwrite.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/pkwrite.h"/>
            <file name="psa_util_internal.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/psa_util_internal.h"/>
            <file name="rsa_alt_helpers.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/rsa_alt_helpers.h"/>
            <file name="ssl_debug_helpers.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ssl_debug_helpers.h"/>
            <file name="ssl_client.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ssl_client.h"/>
            <file name="ssl_misc.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ssl_misc.h"/>
            <file name="ssl_tls13_invasive.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ssl_tls13_invasive.h"/>
            <file name="ssl_tls13_keys.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/ssl_tls13_keys.h"/>
            <file name="psa_crypto_client.c" linked="false" uri="studio:/sdk/util/third_party/mbedtls/library/psa_crypto_client.c"/>
          </folder>
          <folder name="include">
            <folder name="mbedtls">
              <file name="aes.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/aes.h"/>
              <file name="aria.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/aria.h"/>
              <file name="asn1.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/asn1.h"/>
              <file name="asn1write.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/asn1write.h"/>
              <file name="base64.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/base64.h"/>
              <file name="bignum.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/bignum.h"/>
              <file name="build_info.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/build_info.h"/>
              <file name="camellia.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/camellia.h"/>
              <file name="ccm.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ccm.h"/>
              <file name="chacha20.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/chacha20.h"/>
              <file name="chachapoly.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/chachapoly.h"/>
              <file name="check_config.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/check_config.h"/>
              <file name="cipher.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/cipher.h"/>
              <file name="cmac.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/cmac.h"/>
              <file name="compat-2.x.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/compat-2.x.h"/>
              <file name="config_adjust_legacy_crypto.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h"/>
              <file name="config_adjust_legacy_from_psa.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_from_psa.h"/>
              <file name="config_adjust_psa_from_legacy.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h"/>
              <file name="config_adjust_psa_superset_legacy.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h"/>
              <file name="config_adjust_ssl.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_ssl.h"/>
              <file name="config_adjust_x509.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_adjust_x509.h"/>
              <file name="config_psa.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/config_psa.h"/>
              <file name="constant_time.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/constant_time.h"/>
              <file name="ctr_drbg.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ctr_drbg.h"/>
              <file name="debug.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/debug.h"/>
              <file name="des.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/des.h"/>
              <file name="dhm.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/dhm.h"/>
              <file name="ecdh.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ecdh.h"/>
              <file name="ecdsa.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ecdsa.h"/>
              <file name="ecjpake.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ecjpake.h"/>
              <file name="ecp.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ecp.h"/>
              <file name="entropy.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/entropy.h"/>
              <file name="error.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/error.h"/>
              <file name="gcm.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/gcm.h"/>
              <file name="hkdf.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/hkdf.h"/>
              <file name="hmac_drbg.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/hmac_drbg.h"/>
              <file name="lms.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/lms.h"/>
              <file name="mbedtls_config.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/mbedtls_config.h"/>
              <file name="md.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/md.h"/>
              <file name="md5.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/md5.h"/>
              <file name="memory_buffer_alloc.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/memory_buffer_alloc.h"/>
              <file name="net_sockets.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/net_sockets.h"/>
              <file name="nist_kw.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/nist_kw.h"/>
              <file name="oid.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/oid.h"/>
              <file name="pem.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/pem.h"/>
              <file name="pk.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/pk.h"/>
              <file name="pkcs12.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/pkcs12.h"/>
              <file name="pkcs5.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/pkcs5.h"/>
              <file name="pkcs7.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/pkcs7.h"/>
              <file name="platform.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/platform.h"/>
              <file name="platform_time.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/platform_time.h"/>
              <file name="platform_util.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/platform_util.h"/>
              <file name="poly1305.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/poly1305.h"/>
              <file name="private_access.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/private_access.h"/>
              <file name="psa_util.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/psa_util.h"/>
              <file name="ripemd160.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ripemd160.h"/>
              <file name="rsa.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/rsa.h"/>
              <file name="sha1.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/sha1.h"/>
              <file name="sha256.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/sha256.h"/>
              <file name="sha3.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/sha3.h"/>
              <file name="sha512.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/sha512.h"/>
              <file name="ssl.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ssl.h"/>
              <file name="ssl_cache.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ssl_cache.h"/>
              <file name="ssl_ciphersuites.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ssl_ciphersuites.h"/>
              <file name="ssl_cookie.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ssl_cookie.h"/>
              <file name="ssl_ticket.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/ssl_ticket.h"/>
              <file name="threading.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/threading.h"/>
              <file name="timing.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/timing.h"/>
              <file name="version.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/version.h"/>
              <file name="x509.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/x509.h"/>
              <file name="x509_crl.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/x509_crl.h"/>
              <file name="x509_crt.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/x509_crt.h"/>
              <file name="x509_csr.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/mbedtls/x509_csr.h"/>
            </folder>
            <folder name="psa">
              <file name="crypto.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto.h"/>
              <file name="crypto_adjust_auto_enabled.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_adjust_auto_enabled.h"/>
              <file name="crypto_adjust_config_key_pair_types.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h"/>
              <file name="crypto_adjust_config_synonyms.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_adjust_config_synonyms.h"/>
              <file name="build_info.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/build_info.h"/>
              <file name="crypto_builtin_composites.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_builtin_composites.h"/>
              <file name="crypto_builtin_primitives.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_builtin_primitives.h"/>
              <file name="crypto_builtin_key_derivation.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_builtin_key_derivation.h"/>
              <file name="crypto_compat.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_compat.h"/>
              <file name="crypto_config.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_config.h"/>
              <file name="crypto_driver_common.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_driver_common.h"/>
              <file name="crypto_driver_contexts_composites.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_driver_contexts_composites.h"/>
              <file name="crypto_driver_contexts_key_derivation.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h"/>
              <file name="crypto_driver_contexts_primitives.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_driver_contexts_primitives.h"/>
              <file name="crypto_extra.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_extra.h"/>
              <file name="crypto_legacy.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_legacy.h"/>
              <file name="crypto_platform.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_platform.h"/>
              <file name="crypto_se_driver.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_se_driver.h"/>
              <file name="crypto_sizes.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_sizes.h"/>
              <file name="crypto_struct.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_struct.h"/>
              <file name="crypto_types.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_types.h"/>
              <file name="crypto_values.h" linked="false" uri="studio:/sdk/util/third_party/mbedtls/include/psa/crypto_values.h"/>
            </folder>
          </folder>
        </folder>
      </folder>
    </folder>
  </folder>
  <file name="main.c" linked="false" uri="studio:/project/main.c"/>
  <file name="app_init.c" linked="false" uri="studio:/project/app_init.c"/>
  <file name="app_process.c" linked="false" uri="studio:/project/app_process.c"/>
  <file name="app_init.h" linked="false" uri="studio:/project/app_init.h"/>
  <file name="app_process.h" linked="false" uri="studio:/project/app_process.h"/>
  <file name="readme.md" linked="false" uri="studio:/project/readme.md"/>
  <includePath uri="studio:/project/"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/Device/SiliconLabs/EFR32FG23/Include"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/app/common/util/app_assert"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/common/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/hardware/board/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/clock_manager/src"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/CMSIS/Core/Include"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/hardware/driver/configuration_over_swo/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/driver/debug/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/device_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/device_init/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/dmadrv/inc/s2_signals"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/common/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/emlib/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/peripheral/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/hfxo_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/interrupt_manager/inc/arm"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/iostream/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/driver/leddrv/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/config/preset"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_mbedtls_support/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/include"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/util/third_party/mbedtls/library"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/memory_manager/src"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/mpu/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/power_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/sl_psa_driver/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/common"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ble"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/ieee802154"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/wmbus"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/zwave"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/chip/efr32/efr32xg2x"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/protocol/sidewalk"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_callbacks"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/pa-conversions/efr32xg23"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_power_manager_init"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_protocol"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_pti"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/plugin/rail_util_rssi"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/security/sl_component/se_manager/src"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/simple_rail_assistance"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_flex_rail_channel_selector"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/common/toolchain/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/system/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/app/flex/component/rail/sl_wmbus_support"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/sleeptimer/inc"/>
  <includePath uri="studio:/project/simplicity_sdk_2024.6.2/platform/service/udelay/inc"/>
  <macroDefinition name="EFR32FG23B010F512IM48"/>
  <macroDefinition name="HARDWARE_BOARD_DEFAULT_RF_BAND_868"/>
  <macroDefinition name="HARDWARE_BOARD_SUPPORTS_2_RF_BANDS"/>
  <macroDefinition name="HARDWARE_BOARD_SUPPORTS_RF_BAND_868"/>
  <macroDefinition name="HARDWARE_BOARD_SUPPORTS_RF_BAND_915"/>
  <macroDefinition name="HFXO_FREQ" value="39000000"/>
  <macroDefinition name="SL_BOARD_NAME" value="&quot;BRD2600A&quot;"/>
  <macroDefinition name="SL_BOARD_REV" value="&quot;A01&quot;"/>
  <macroDefinition name="SL_CLOCK_MANAGER_AUTO_BAND_VALID" value="1"/>
  <macroDefinition name="SL_COMPONENT_CATALOG_PRESENT"/>
  <macroDefinition name="SL_CODE_COMPONENT_PERIPHERAL_SYSRTC" value="hal_sysrtc"/>
  <macroDefinition name="CMSIS_NVIC_VIRTUAL"/>
  <macroDefinition name="CMSIS_NVIC_VIRTUAL_HEADER_FILE" value="&quot;cmsis_nvic_virtual.h&quot;"/>
  <macroDefinition name="MBEDTLS_CONFIG_FILE" value="&lt;sl_mbedtls_config.h>"/>
  <macroDefinition name="SL_MEMORY_POOL_LIGHT"/>
  <macroDefinition name="SL_CODE_COMPONENT_POWER_MANAGER" value="power_manager"/>
  <macroDefinition name="MBEDTLS_PSA_CRYPTO_CLIENT"/>
  <macroDefinition name="MBEDTLS_PSA_CRYPTO_CONFIG_FILE" value="&lt;psa_crypto_config.h>"/>
  <macroDefinition name="SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT" value="0"/>
  <macroDefinition name="SL_RAIL_UTIL_PA_CONFIG_HEADER" value="&lt;sl_rail_util_pa_config.h>"/>
  <macroDefinition name="SL_CODE_COMPONENT_CORE" value="core"/>
  <macroDefinition name="SL_CODE_COMPONENT_SLEEPTIMER" value="sleeptimer"/>
  <libraryFile uri="studio:/project/simplicity_sdk_2024.6.2/platform/emdrv/nvm3/lib/libnvm3_CM33_gcc.a"/>
  <libraryFile uri="studio:/project/simplicity_sdk_2024.6.2/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg23_gcc_release.a"/>
  <libraryFile name="gcc"/>
  <libraryFile name="c"/>
  <libraryFile name="m"/>
  <libraryFile name="nosys"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.optimization" value="debug"/>
  <toolOption toolId="toolOption.uc.toolchain" optionId="toolOption.uc.toolchain.cpu" value="cortex-m33"/>
  <toolOption toolId="toolOption.uc.toolchain" optionId="toolOption.uc.toolchain.fpu" value="fpv5-sp"/>
  <toolOption toolId="toolOption.uc.toolchain" optionId="toolOption.uc.toolchain.float_abi" value="hard"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.preinclude">
    <toolListOption value="sl_gcc_preinclude.h"/>
  </toolOption>
  <toolOption toolId="toolOption.uc.assembler" optionId="toolOption.uc.assembler.preinclude">
    <toolListOption value="sl_gcc_preinclude.h"/>
  </toolOption>
  <toolOption toolId="toolOption.uc.linker" optionId="toolOption.uc.linker.linkerScript" value="$project/autogen/linkerfile.ld"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.function_sections" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.data_sections" value="true"/>
  <toolOption toolId="toolOption.uc.linker" optionId="toolOption.uc.linker.nano_c_libs" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.omit_frame_pointer" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.no_exceptions" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.no_rtti" value="true"/>
  <toolOption toolId="toolOption.uc.linker" optionId="toolOption.uc.linker.generateMapFile" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.c_standard" value="c18"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.cxx_standard" value="c++17"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.warnings.all" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.warnings.extra" value="true"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.warnings.pedantic" value="false"/>
  <toolOption toolId="toolOption.uc.compiler" optionId="toolOption.uc.compiler.warnings.are_errors" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type" value="floatingpoint.type.hard"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type" value="floatingpoint.type.hard"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type" value="floatingpoint.type.hard"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type" value="floatingpoint.type.hard"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type" value="floatingpoint.type.hard"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.preinclude">
    <toolListOption value="sl_gcc_preinclude.h"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.preinclude">
    <toolListOption value="sl_gcc_preinclude.h"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.preinclude">
    <toolListOption value="sl_gcc_preinclude.h"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect" value="gnu.c.compiler.dialect.c18"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.cpp17"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level.debug"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level.debug"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="gnu.c.compiler.option.warnings.allwarn" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="gnu.cpp.compiler.option.warnings.allwarn" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="gnu.c.compiler.option.warnings.extrawarn" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="gnu.cpp.compiler.option.warnings.extrawarn" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="gnu.c.compiler.option.warnings.toerrors" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="gnu.cpp.compiler.option.warnings.toerrors" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="gnu.c.compiler.option.warnings.pedantic" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="gnu.cpp.compiler.option.warnings.pedantic" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script" value="${workspace_loc:/${ProjName}/autogen/linkerfile.ld}"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map" value="true"/>
  <toolOption toolId="studio.ide.project" optionId="device_opn" value="efr32fg23b010f512im48"/>
  <toolOption toolId="makefile.arm.gcc.compiler" optionId="misc">
    <toolListOption value="-mcmse"/>
    <toolListOption value="--specs=nano.specs"/>
    <toolListOption value="-g"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base" optionId="com.silabs.gnu.c.compiler.option.misc.otherlist">
    <toolListOption value="-mcmse"/>
    <toolListOption value="--specs=nano.specs"/>
    <toolListOption value="-c"/>
    <toolListOption value="-fmessage-length=0"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base" optionId="com.silabs.gnu.cpp.compiler.option.misc.otherlist">
    <toolListOption value="-mcmse"/>
    <toolListOption value="--specs=nano.specs"/>
    <toolListOption value="-c"/>
    <toolListOption value="-fmessage-length=0"/>
  </toolOption>
  <toolOption toolId="iar.ewp.arm.compiler" optionId="IExtraOptions">
    <toolListOption value="--cmse"/>
  </toolOption>
  <toolOption toolId="iar.arm.toolchain.compiler.v5.4.1" optionId="iar.arm.toolchain.compiler.option.additionalUserOptions">
    <toolListOption value="--cmse"/>
  </toolOption>
  <toolOption toolId="makefile.arm.gcc.linker" optionId="misc">
    <toolListOption value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
    <toolListOption value="-Wl,--gc-sections"/>
    <toolListOption value="-Wl,--no-warn-rwx-segments"/>
  </toolOption>
  <toolOption toolId="makefile.host.gcc.linker" optionId="misc">
    <toolListOption value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.gnu.c.link.option.ldflags">
    <toolListOption value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
    <toolListOption value="-Wl,--no-warn-rwx-segments"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base" optionId="com.silabs.gnu.cpp.link.option.flags">
    <toolListOption value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
    <toolListOption value="-Wl,--no-warn-rwx-segments"/>
  </toolOption>
  <toolOption toolId="makefile.host.gcc.compiler" optionId="misc">
    <toolListOption value="--specs=nano.specs"/>
  </toolOption>
  <toolOption toolId="makefile.arm.gcc.assembler" optionId="misc">
    <toolListOption value="-x assembler-with-cpp"/>
  </toolOption>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency" value="true"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs" value="false"/>
  <toolOption toolId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker" optionId="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency" value="true"/>
</project>
