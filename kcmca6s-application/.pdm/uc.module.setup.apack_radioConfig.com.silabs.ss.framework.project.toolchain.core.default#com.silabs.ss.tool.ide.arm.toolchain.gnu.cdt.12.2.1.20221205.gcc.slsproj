<?xml version="1.0" encoding="UTF-8"?>
<project name="uc.module.setup.apack_radioConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc" propertyScope="project">
  <folder name="autogen">
    <file name="radioconf_generation_log.json" builtin="true" generated="true"/>
    <file name="rail_config.c" builtin="true" generated="true"/>
    <file name="rail_config.h" builtin="true" generated="true"/>
  </folder>
  <includePath uri="studio:/project/autogen"/>
</project>
