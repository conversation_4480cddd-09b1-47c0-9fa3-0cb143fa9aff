<?xml version="1.0" encoding="UTF-8"?>
<project name="uc.module.setup.ucConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc" propertyScope="project">
  <folder name="config">
    <folder name="rail">
      <file name="radio_settings.radioconf" generated="true" linked="false"/>
      <file name="profile_wmbus.restriction" generated="true" linked="false"/>
    </folder>
    <file name="sl_memory_config.h" generated="true" linked="false"/>
    <file name="app_assert_config.h" generated="true" linked="false"/>
    <file name="sl_board_control_config.h" generated="true" linked="false"/>
    <file name="sl_clock_manager_oscillator_config.h" generated="true" linked="false"/>
    <file name="sl_clock_manager_tree_config.h" generated="true" linked="false"/>
    <file name="sl_power_manager_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_init_inst0_config.h" generated="true" linked="false"/>
    <file name="sl_wmbus_support_config.h" generated="true" linked="false"/>
    <file name="nvm3_default_config.h" generated="true" linked="false"/>
    <file name="psa_crypto_config.h" generated="true" linked="false"/>
    <file name="sl_device_init_dcdc_config.h" generated="true" linked="false"/>
    <file name="sl_memory_manager_config.h" generated="true" linked="false"/>
    <file name="sl_simple_led_led0_config.h" generated="true" linked="false"/>
    <file name="sl_hfxo_manager_config.h" generated="true" linked="false"/>
    <file name="dmadrv_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_pti_config.h" generated="true" linked="false"/>
    <file name="sl_interrupt_manager_s2_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_rssi_config.h" generated="true" linked="false"/>
    <file name="sl_flex_rail_config.h" generated="true" linked="false"/>
    <file name="sl_memory_manager_region_config.h" generated="true" linked="false"/>
    <file name="sl_debug_swo_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_power_manager_init_config.h" generated="true" linked="false"/>
    <file name="sl_iostream_eusart_vcom_config.h" generated="true" linked="false"/>
    <file name="sl_flex_rail_channel_selector_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_pa_config.h" generated="true" linked="false"/>
    <file name="sl_core_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_protocol_config.h" generated="true" linked="false"/>
    <file name="emlib_core_debug_config.h" generated="true" linked="false"/>
    <file name="sl_sleeptimer_config.h" generated="true" linked="false"/>
    <file name="sl_mbedtls_config.h" generated="true" linked="false"/>
    <file name="sl_mbedtls_device_config.h" generated="true" linked="false"/>
    <file name="sl_rail_util_callbacks_config.h" generated="true" linked="false"/>
  </folder>
  <includePath uri="studio:/project/config"/>
  <includePath uri="studio:/project/config/rail"/>
</project>
